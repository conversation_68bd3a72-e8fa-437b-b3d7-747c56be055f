const express = require('express');
const router = express.Router();
const teacherAnalyticsController = require('../controllers/teacherAnalyticsController');
const {
    authenticateToken,
    authorize,
} = require('../middleware/authMiddleware');

/**
 * TEACHER ANALYTICS ROUTES
 * Các API chuyên dành cho giảng viên phân tích quiz và học sinh
 */

/**
 * GET /api/teacher-analytics/quiz/:quizId/comprehensive-report
 * Lấy báo cáo tổng quan chi tiết về quiz
 * - Phân tích điểm mạnh/yếu theo LO và Level
 * - Phân nhóm học sinh theo performance
 * - Insights và recommendations cho giảng viên
 * - Chỉ teacher và admin mới có quyền truy cập
 */
router.get(
    '/quiz/:quizId/comprehensive-report',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getComprehensiveQuizReport
);

/**
 * GET /api/teacher-analytics/quiz/:quizId/student-groups/:groupType
 * Lấy phân tích chi tiết theo nhóm học sinh
 * groupType: excellent, good, average, weak
 * - Chi tiết từng nhóm học sinh
 * - Phân tích LO/Level riêng cho nhóm
 * - Recommendations cụ thể cho từng nhóm
 */
router.get(
    '/quiz/:quizId/student-groups/:groupType',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getStudentGroupAnalysis
);

/**
 * GET /api/teacher-analytics/quiz-comparison
 * So sánh performance giữa các quiz
 * Query params:
 * - quiz_ids: danh sách quiz_id cách nhau bởi dấu phẩy (ví dụ: 1,2,3)
 * - subject_id: so sánh tất cả quiz trong subject
 * - Benchmark và insights so sánh
 */
router.get(
    '/quiz-comparison',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getQuizComparison
);

/**
 * GET /api/teacher-analytics/quiz/:quizId/student/:userId/detailed-analysis
 * Lấy phân tích chi tiết cá nhân học sinh
 * - Phân tích từng câu hỏi với insights
 * - Phân tích theo LO với recommendations
 * - Tổng kết performance và đề xuất cải thiện
 */
router.get(
    '/quiz/:quizId/student/:userId/detailed-analysis',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getStudentDetailedAnalysis
);

/**
 * GET /api/teacher-analytics/quiz/:quizId/teaching-insights
 * Lấy insights và recommendations tổng hợp cho giảng viên
 * - Phân tích curriculum và phương pháp giảng dạy
 * - Insights về học sinh và recommendations hành động
 * - Priority actions và timeline thực hiện
 */
router.get(
    '/quiz/:quizId/teaching-insights',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getTeachingInsights
);

/**
 * GET /api/teacher-analytics/quiz/:quizId/benchmark
 * Lấy benchmark và so sánh với historical data
 * Query params:
 * - compare_with_subject: so sánh với các quiz khác trong subject (default: true)
 * - compare_with_teacher: so sánh với quiz của cùng giảng viên (default: true)
 * - Performance ranking và insights so sánh
 */
router.get(
    '/quiz/:quizId/benchmark',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.getQuizBenchmark
);

/**
 * GET /api/teacher-analytics/debug/:quizId
 * Debug API để kiểm tra dữ liệu quiz
 * - Kiểm tra quiz, quiz results, question history
 * - Test phân nhóm học sinh
 * - Chỉ dành cho debug, có thể xóa sau khi fix
 */
router.get(
    '/debug/:quizId',
    authenticateToken,
    authorize(['admin', 'teacher']),
    teacherAnalyticsController.debugQuizData
);

/**
 * GET /api/teacher-analytics/debug/subject/:subjectId/quizzes
 * Debug endpoint để kiểm tra tất cả quiz trong subject
 */
router.get(
    '/debug/subject/:subjectId/quizzes',
    authenticateToken,
    authorize(['admin', 'teacher']),
    async (req, res) => {
        try {
            const { Quiz, Subject } = require('../models');
            const subjectId = req.params.subjectId;

            // Lấy tất cả quiz trong subject
            const quizzes = await Quiz.findAll({
                where: { subject_id: subjectId },
                attributes: ['quiz_id', 'name', 'status', 'update_time'],
                include: [
                    {
                        model: Subject,
                        attributes: ['subject_id', 'name']
                    }
                ],
                order: [['update_time', 'DESC']]
            });

            // Phân loại theo status
            const statusGroups = {};
            quizzes.forEach(quiz => {
                if (!statusGroups[quiz.status]) {
                    statusGroups[quiz.status] = [];
                }
                statusGroups[quiz.status].push({
                    quiz_id: quiz.quiz_id,
                    name: quiz.name,
                    update_time: quiz.update_time
                });
            });

            res.json({
                success: true,
                data: {
                    subject_id: subjectId,
                    subject_name: quizzes[0]?.Subject?.name || 'Unknown',
                    total_quizzes: quizzes.length,
                    status_breakdown: statusGroups,
                    all_quizzes: quizzes.map(q => ({
                        quiz_id: q.quiz_id,
                        name: q.name,
                        status: q.status,
                        update_time: q.update_time
                    }))
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }
);

module.exports = router;

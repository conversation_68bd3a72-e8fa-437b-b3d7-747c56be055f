# Postman Testing Guide - Emoji & Social Interaction System

## Prerequisites

1. **Database Setup**: Run the SQL schema from `gamification_emoji_social_system.sql`
2. **Server Running**: Start the backend server (`npm start` or `npm run dev`)
3. **Authentication**: Get a valid JWT token by logging in through `/api/users/login`

## Authentication Setup

### Step 1: Login to get JWT token
```
POST http://localhost:3000/api/users/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### Step 2: Set Authorization Header
For all subsequent requests, add:
```
Authorization: Bearer <your_jwt_token>
```

---

## 🧪 TESTING SEQUENCE

### Phase 1: Initialize Emoji System

#### 1.1 Initialize User Emoji System
```
POST http://localhost:3000/api/emojis/initialize
Authorization: Bearer <token>
```

**Expected Result:**
- User gets basic emojis for their current tier
- UserSocialStats record created
- Response shows unlocked emojis count

#### 1.2 Verify User Collection
```
GET http://localhost:3000/api/emojis/collection
Authorization: Bearer <token>
```

**Expected Result:**
- Shows user's emoji collection
- Includes emoji details and usage stats
- Displays emojis grouped by category/rarity

### Phase 2: Emoji Usage Testing

#### 2.1 Use Emoji in Quiz Context
```
POST http://localhost:3000/api/emojis/use
Authorization: Bearer <token>
Content-Type: application/json

{
  "emoji_type_id": 1,
  "context": "POST_QUIZ",
  "metadata": {
    "quiz_score": 95,
    "celebration": true
  }
}
```

#### 2.2 Use Emoji for Social Interaction
```
POST http://localhost:3000/api/emojis/use
Authorization: Bearer <token>
Content-Type: application/json

{
  "emoji_type_id": 2,
  "context": "SOCIAL_REACTION",
  "target_user_id": 2,
  "metadata": {
    "reaction_type": "encouragement"
  }
}
```

#### 2.3 Check Usage History
```
GET http://localhost:3000/api/emojis/usage/history?limit=10
Authorization: Bearer <token>
```

#### 2.4 Check Usage Statistics
```
GET http://localhost:3000/api/emojis/usage/stats?timeframe=7d
Authorization: Bearer <token>
```

### Phase 3: Social Interaction Testing

#### 3.1 Send Emoji Reaction to Another User
```
POST http://localhost:3000/api/social/emoji-reaction
Authorization: Bearer <token>
Content-Type: application/json

{
  "to_user_id": 2,
  "emoji_type_id": 3,
  "context": "quiz_completion",
  "metadata": {
    "message": "Great job on the quiz!"
  }
}
```

#### 3.2 Send Encouragement
```
POST http://localhost:3000/api/social/encouragement
Authorization: Bearer <token>
Content-Type: application/json

{
  "to_user_id": 2,
  "context": "quiz_attempt",
  "message": "You can do it! Keep trying!"
}
```

#### 3.3 Celebrate Achievement
```
POST http://localhost:3000/api/social/celebrate
Authorization: Bearer <token>
Content-Type: application/json

{
  "to_user_id": 2,
  "achievement_type": "level_up",
  "emoji_type_id": 4,
  "message": "Congratulations on leveling up!"
}
```

#### 3.4 Check Social Stats
```
GET http://localhost:3000/api/social/stats?timeframe=7d
Authorization: Bearer <token>
```

### Phase 4: Emoji Shop Testing

#### 4.1 View Emoji Shop
```
GET http://localhost:3000/api/emojis/shop
Authorization: Bearer <token>
```

**Expected Result:**
- Shows purchasable emojis for user's tier
- Displays user's Kristal balance
- Filters out already owned emojis

#### 4.2 Purchase Emoji with Kristal
```
POST http://localhost:3000/api/emojis/purchase
Authorization: Bearer <token>
Content-Type: application/json

{
  "emoji_type_id": 25
}
```

**Expected Result:**
- Deducts Kristal from user balance
- Unlocks emoji for user
- Creates currency transaction record

### Phase 5: Leaderboard & Rankings

#### 5.1 Get Social Leaderboard
```
GET http://localhost:3000/api/social/leaderboard?criteria=reputation&limit=10
Authorization: Bearer <token>
```

#### 5.2 Get User's Social Rank
```
GET http://localhost:3000/api/social/rank?criteria=reputation
Authorization: Bearer <token>
```

#### 5.3 Get Top Social Users
```
GET http://localhost:3000/api/social/top-users?timeframe=7d&limit=5
Authorization: Bearer <token>
```

### Phase 6: Profile & Favorites

#### 6.1 Set Favorite Emoji
```
POST http://localhost:3000/api/emojis/favorite
Authorization: Bearer <token>
Content-Type: application/json

{
  "emoji_type_id": 5
}
```

#### 6.2 View Social Profile
```
GET http://localhost:3000/api/social/profile
Authorization: Bearer <token>
```

#### 6.3 View Another User's Profile
```
GET http://localhost:3000/api/social/profile/2
Authorization: Bearer <token>
```

---

## 🔍 VALIDATION CHECKLIST

### Database Validation
After each test, verify in database:

1. **EmojiUsageHistory**: Check usage records are created
2. **SocialInteractions**: Verify social interactions are logged
3. **UserSocialStats**: Confirm stats are updated correctly
4. **UserEmojis**: Check emoji unlocks and favorites
5. **CurrencyTransactions**: Verify Kristal purchases

### Response Validation
Check each response for:

1. **Success Status**: `success: true`
2. **Proper Message**: Descriptive success message
3. **Data Structure**: Correct data format
4. **Associations**: Related data properly loaded
5. **Computed Fields**: Calculated values (reputation_level, etc.)

### Error Testing
Test error scenarios:

1. **Invalid Emoji ID**: Use non-existent emoji_type_id
2. **Insufficient Kristal**: Try purchasing expensive emoji
3. **Self-Interaction**: Send reaction to same user
4. **Unauthorized Access**: Test without JWT token
5. **Invalid Context**: Use invalid usage context

---

## 📊 EXPECTED METRICS

After running all tests, you should see:

### User Social Stats
- `total_emojis_unlocked`: Increased count
- `total_emoji_usage`: Usage count incremented
- `positive_interactions_sent`: Social interactions sent
- `positive_interactions_received`: Interactions received
- `social_reputation_score`: Calculated score (0-100)

### System Analytics
- Emoji usage patterns by context
- Social interaction frequency
- Popular emojis and categories
- User engagement metrics

---

## 🚨 TROUBLESHOOTING

### Common Issues

1. **Authentication Errors**
   - Verify JWT token is valid and not expired
   - Check Authorization header format

2. **Database Errors**
   - Ensure all tables are created from SQL schema
   - Check foreign key constraints

3. **Model Association Errors**
   - Verify all models are properly imported
   - Check association definitions

4. **Validation Errors**
   - Check required fields in request body
   - Verify data types and formats

### Debug Tips

1. Check server console for detailed error logs
2. Use database queries to verify data changes
3. Test with different user accounts
4. Verify tier progression and currency balances

---

## 📈 PERFORMANCE TESTING

For production readiness, test:

1. **Concurrent Users**: Multiple users using emojis simultaneously
2. **High Frequency Usage**: Rapid emoji usage in quiz sessions
3. **Large Data Sets**: Users with many emojis and interactions
4. **Database Performance**: Query optimization for leaderboards

---

## ✅ SUCCESS CRITERIA

The system passes testing when:

1. All API endpoints return expected responses
2. Database records are created/updated correctly
3. Social interactions are properly tracked
4. Emoji unlocking works for all methods
5. Leaderboards and rankings function correctly
6. Error handling works for edge cases
7. Performance is acceptable under load

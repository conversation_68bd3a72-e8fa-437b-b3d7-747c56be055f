const {
    QuizR<PERSON>ult,
    UserQuestionHistory,
    Question,
    Quiz,
    Subject,
    User,
    LO,
    Level,
    Chapter,
    ChapterLO,
    Answer,
    Course,
    Program
} = require('../models');
const { Op, Sequelize } = require('sequelize');

/**
 * TEACHER ANALYTICS CONTROLLER
 * <PERSON>yên cung cấp các API analytics chi tiết cho giảng viên
 */

// ==================== HELPER FUNCTIONS ====================

/**
 * Tính toán phân tích điểm mạnh/yếu theo LO
 */
const analyzeLOPerformance = (questions, questionHistory) => {
    const loStats = {};

    questions.forEach(question => {
        const loId = question.lo_id;
        const loName = question.LO?.name || 'Unknown LO';

        if (!loStats[loId]) {
            loStats[loId] = {
                lo_id: loId,
                lo_name: loName,
                total_questions: 0,
                total_attempts: 0,
                correct_attempts: 0,
                total_time: 0,
                students_attempted: new Set()
            };
        }

        const questionAttempts = questionHistory.filter(h => h.question_id === question.question_id);

        loStats[loId].total_questions++;
        loStats[loId].total_attempts += questionAttempts.length;
        loStats[loId].correct_attempts += questionAttempts.filter(h => h.is_correct).length;
        loStats[loId].total_time += questionAttempts.reduce((sum, h) => sum + (h.time_spent || 0), 0);

        questionAttempts.forEach(h => loStats[loId].students_attempted.add(h.user_id));
    });

    // Tính toán metrics và phân loại
    const loAnalysis = Object.values(loStats).map(lo => {
        const accuracy = lo.total_attempts > 0 ? (lo.correct_attempts / lo.total_attempts) * 100 : 0;
        const avgTime = lo.total_attempts > 0 ? lo.total_time / lo.total_attempts : 0;
        const studentCount = lo.students_attempted.size;

        let performance_level = 'good';
        let insights = [];
        let recommendations = [];

        if (accuracy < 50) {
            performance_level = 'weak';
            insights.push(`Tỷ lệ đúng thấp (${accuracy.toFixed(1)}%) - cần cải thiện`);
            recommendations.push('Cần bổ sung thêm bài giảng và bài tập cho LO này');
            recommendations.push('Xem xét điều chỉnh phương pháp giảng dạy');
        } else if (accuracy < 70) {
            performance_level = 'average';
            insights.push(`Tỷ lệ đúng trung bình (${accuracy.toFixed(1)}%) - có thể cải thiện`);
            recommendations.push('Tăng cường luyện tập thêm cho LO này');
        } else {
            insights.push(`Tỷ lệ đúng tốt (${accuracy.toFixed(1)}%)`);
            recommendations.push('Duy trì phương pháp giảng dạy hiện tại');
        }

        if (avgTime > 120) { // > 2 phút
            insights.push('Thời gian trả lời cao - có thể câu hỏi khó hoặc chưa hiểu rõ');
            recommendations.push('Xem xét đơn giản hóa câu hỏi hoặc bổ sung giải thích');
        }

        return {
            ...lo,
            students_attempted: studentCount,
            accuracy: parseFloat(accuracy.toFixed(2)),
            average_time: parseFloat(avgTime.toFixed(2)),
            performance_level,
            insights,
            recommendations
        };
    });

    // Sắp xếp theo độ yếu (accuracy thấp nhất trước)
    return loAnalysis.sort((a, b) => a.accuracy - b.accuracy);
};

/**
 * Tính toán phân tích điểm mạnh/yếu theo Level
 */
const analyzeLevelPerformance = (questions, questionHistory) => {
    const levelStats = {};

    questions.forEach(question => {
        const levelId = question.level_id;
        const levelName = question.Level?.name || 'Unknown Level';

        if (!levelStats[levelId]) {
            levelStats[levelId] = {
                level_id: levelId,
                level_name: levelName,
                total_questions: 0,
                total_attempts: 0,
                correct_attempts: 0,
                total_time: 0,
                students_attempted: new Set()
            };
        }

        const questionAttempts = questionHistory.filter(h => h.question_id === question.question_id);

        levelStats[levelId].total_questions++;
        levelStats[levelId].total_attempts += questionAttempts.length;
        levelStats[levelId].correct_attempts += questionAttempts.filter(h => h.is_correct).length;
        levelStats[levelId].total_time += questionAttempts.reduce((sum, h) => sum + (h.time_spent || 0), 0);

        questionAttempts.forEach(h => levelStats[levelId].students_attempted.add(h.user_id));
    });

    const levelAnalysis = Object.values(levelStats).map(level => {
        const accuracy = level.total_attempts > 0 ? (level.correct_attempts / level.total_attempts) * 100 : 0;
        const avgTime = level.total_attempts > 0 ? level.total_time / level.total_attempts : 0;
        const studentCount = level.students_attempted.size;

        let performance_level = 'good';
        let insights = [];
        let recommendations = [];

        // Phân tích dựa trên level
        if (level.level_name.toLowerCase().includes('easy') || level.level_name.toLowerCase().includes('dễ')) {
            if (accuracy < 80) {
                performance_level = 'weak';
                insights.push(`Câu hỏi dễ nhưng tỷ lệ đúng thấp (${accuracy.toFixed(1)}%) - cần xem xét kiến thức cơ bản`);
                recommendations.push('Cần củng cố kiến thức nền tảng');
            }
        } else if (level.level_name.toLowerCase().includes('hard') || level.level_name.toLowerCase().includes('khó')) {
            if (accuracy < 40) {
                performance_level = 'weak';
                insights.push(`Câu hỏi khó với tỷ lệ đúng rất thấp (${accuracy.toFixed(1)}%)`);
                recommendations.push('Cần bổ sung thêm ví dụ và bài tập nâng cao');
            } else if (accuracy > 70) {
                insights.push(`Học sinh làm tốt câu hỏi khó (${accuracy.toFixed(1)}%)`);
                recommendations.push('Có thể tăng độ khó hoặc thêm câu hỏi thách thức');
            }
        }

        return {
            ...level,
            students_attempted: studentCount,
            accuracy: parseFloat(accuracy.toFixed(2)),
            average_time: parseFloat(avgTime.toFixed(2)),
            performance_level,
            insights,
            recommendations
        };
    });

    return levelAnalysis.sort((a, b) => a.accuracy - b.accuracy);
};

/**
 * Phân nhóm học sinh theo performance
 */
const categorizeStudentsByPerformance = (quizResults, questionHistory) => {
    const studentGroups = {
        excellent: { threshold: 85, students: [], insights: [] },
        good: { threshold: 70, students: [], insights: [] },
        average: { threshold: 50, students: [], insights: [] },
        weak: { threshold: 0, students: [], insights: [] }
    };

    quizResults.forEach(result => {
        const score = result.score;
        const studentHistory = questionHistory.filter(h => h.user_id === result.user_id);
        const avgTime = studentHistory.length > 0 ?
            studentHistory.reduce((sum, h) => sum + (h.time_spent || 0), 0) / studentHistory.length : 0;

        // Tính phần trăm điểm từ số câu đúng
        const correctAnswers = studentHistory.filter(h => h.is_correct).length;
        const totalQuestions = studentHistory.length;
        const percentageScore = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

        // Sử dụng điểm phần trăm để phân nhóm thay vì điểm thô
        const scoreForGrouping = percentageScore;

        const studentData = {
            user_id: result.user_id,
            name: result.Student?.name || 'Unknown',
            email: result.Student?.email || '',
            score: score, // Giữ nguyên điểm gốc để hiển thị
            percentage_score: parseFloat(percentageScore.toFixed(2)), // Thêm điểm phần trăm
            completion_time: result.completion_time,
            average_time_per_question: parseFloat(avgTime.toFixed(2)),
            total_questions_attempted: studentHistory.length,
            correct_answers: correctAnswers
        };

        if (scoreForGrouping >= 85) {
            studentGroups.excellent.students.push(studentData);
        } else if (scoreForGrouping >= 70) {
            studentGroups.good.students.push(studentData);
        } else if (scoreForGrouping >= 50) {
            studentGroups.average.students.push(studentData);
        } else {
            studentGroups.weak.students.push(studentData);
        }
    });

    // Thêm insights cho từng nhóm
    studentGroups.excellent.insights = [
        'Nhóm học sinh xuất sắc, có thể làm mentor cho nhóm khác',
        'Cân nhắc đưa ra thêm thách thức nâng cao'
    ];

    studentGroups.good.insights = [
        'Nhóm học sinh tốt, cần duy trì và phát triển thêm',
        'Có thể hướng dẫn thêm để đạt mức xuất sắc'
    ];

    studentGroups.average.insights = [
        'Nhóm cần hỗ trợ thêm để cải thiện',
        'Tập trung vào các điểm yếu cụ thể'
    ];

    studentGroups.weak.insights = [
        'Nhóm cần sự quan tâm đặc biệt',
        'Cần kế hoạch hỗ trợ cá nhân hóa',
        'Xem xét phương pháp giảng dạy phù hợp hơn'
    ];

    return studentGroups;
};

/**
 * Tạo insights tổng thể cho giảng viên
 */
const generateTeacherInsights = (quizData, loAnalysis, levelAnalysis, studentGroups) => {
    const insights = [];
    const recommendations = [];

    // Phân tích tổng thể
    const totalStudents = quizData.participant_stats.total_participants;
    const avgScore = quizData.participant_stats.average_score;
    const completionRate = quizData.participant_stats.completion_rate;

    // Insights về tổng thể
    if (avgScore < 50) {
        insights.push({
            type: 'warning',
            category: 'overall_performance',
            message: `Điểm trung bình thấp (${avgScore.toFixed(1)}/100) - cần xem xét lại phương pháp giảng dạy`,
            priority: 'high'
        });
        recommendations.push({
            category: 'teaching_method',
            suggestion: 'Cần điều chỉnh phương pháp giảng dạy và bổ sung thêm tài liệu',
            priority: 'high'
        });
    } else if (avgScore > 85) {
        insights.push({
            type: 'success',
            category: 'overall_performance',
            message: `Điểm trung bình cao (${avgScore.toFixed(1)}/100) - học sinh nắm vững kiến thức`,
            priority: 'medium'
        });
        recommendations.push({
            category: 'challenge',
            suggestion: 'Có thể tăng độ khó hoặc thêm câu hỏi nâng cao',
            priority: 'medium'
        });
    }

    // Insights về completion rate
    if (completionRate < 80) {
        insights.push({
            type: 'warning',
            category: 'engagement',
            message: `Tỷ lệ hoàn thành thấp (${completionRate.toFixed(1)}%) - cần tăng cường động lực học tập`,
            priority: 'high'
        });
        recommendations.push({
            category: 'engagement',
            suggestion: 'Xem xét giảm thời gian quiz hoặc tăng tính tương tác',
            priority: 'high'
        });
    }

    // Insights về phân nhóm học sinh
    const weakStudentCount = studentGroups.weak.students.length;
    const excellentStudentCount = studentGroups.excellent.students.length;

    if (weakStudentCount > totalStudents * 0.3) {
        insights.push({
            type: 'warning',
            category: 'student_distribution',
            message: `Có ${weakStudentCount} học sinh yếu (${((weakStudentCount / totalStudents) * 100).toFixed(1)}%) - cần hỗ trợ đặc biệt`,
            priority: 'high'
        });
        recommendations.push({
            category: 'support',
            suggestion: 'Tổ chức lớp phụ đạo hoặc hỗ trợ cá nhân cho nhóm học sinh yếu',
            priority: 'high'
        });
    }

    if (excellentStudentCount > totalStudents * 0.4) {
        insights.push({
            type: 'success',
            category: 'student_distribution',
            message: `Có ${excellentStudentCount} học sinh xuất sắc (${((excellentStudentCount / totalStudents) * 100).toFixed(1)}%) - kết quả tốt`,
            priority: 'low'
        });
        recommendations.push({
            category: 'advanced',
            suggestion: 'Tận dụng nhóm học sinh giỏi để hỗ trợ nhóm yếu hơn',
            priority: 'medium'
        });
    }

    // Insights về LO yếu nhất
    const weakestLO = loAnalysis.find(lo => lo.performance_level === 'weak');
    if (weakestLO) {
        insights.push({
            type: 'warning',
            category: 'learning_outcome',
            message: `LO "${weakestLO.lo_name}" có hiệu suất thấp nhất (${weakestLO.accuracy}%)`,
            priority: 'high'
        });
        recommendations.push({
            category: 'curriculum',
            suggestion: `Cần tăng cường giảng dạy cho LO "${weakestLO.lo_name}"`,
            priority: 'high'
        });
    }

    return { insights, recommendations };
};

// ==================== API ENDPOINTS ====================

/**
 * API: Lấy báo cáo tổng quan quiz nâng cao cho giảng viên
 * GET /api/teacher-analytics/quiz/:quizId/comprehensive-report
 */
const getComprehensiveQuizReport = async (req, res) => {
    try {
        const quizId = req.params.quizId;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Lấy thông tin quiz với đầy đủ relations
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                },
                {
                    model: Subject,
                    attributes: ['subject_id', 'name', 'description']
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy kết quả quiz
        const quizResults = await QuizResult.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'Student',
                    attributes: ['user_id', 'name', 'email']
                }
            ]
        });

        // Lấy lịch sử trả lời câu hỏi
        const questionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'User',
                    attributes: ['user_id', 'name', 'email']
                },
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id']
                }
            ]
        });

        // Tính toán thống kê cơ bản
        const totalParticipants = quizResults.length;
        const completedParticipants = quizResults.filter(r => r.status === 'completed').length;
        const averageScore = totalParticipants > 0 ?
            quizResults.reduce((acc, curr) => acc + curr.score, 0) / totalParticipants : 0;
        const highestScore = totalParticipants > 0 ? Math.max(...quizResults.map(r => r.score)) : 0;
        const lowestScore = totalParticipants > 0 ? Math.min(...quizResults.map(r => r.score)) : 0;

        const basicStats = {
            quiz_info: {
                quiz_id: quiz.quiz_id,
                name: quiz.name,
                subject_name: quiz.Subject?.name || 'Unknown Subject',
                total_questions: quiz.Questions.length,
                duration: quiz.duration,
                status: quiz.status
            },
            participant_stats: {
                total_participants: totalParticipants,
                completed_participants: completedParticipants,
                completion_rate: totalParticipants > 0 ? (completedParticipants / totalParticipants) * 100 : 0,
                average_score: parseFloat(averageScore.toFixed(2)),
                highest_score: highestScore,
                lowest_score: lowestScore
            }
        };

        // Phân tích chi tiết
        const loAnalysis = analyzeLOPerformance(quiz.Questions, questionHistory);
        const levelAnalysis = analyzeLevelPerformance(quiz.Questions, questionHistory);
        const studentGroups = categorizeStudentsByPerformance(quizResults, questionHistory);
        const teacherInsights = generateTeacherInsights(basicStats, loAnalysis, levelAnalysis, studentGroups);

        return res.status(200).json({
            success: true,
            data: {
                ...basicStats,
                learning_outcome_analysis: loAnalysis,
                difficulty_level_analysis: levelAnalysis,
                student_performance_groups: studentGroups,
                teacher_insights: teacherInsights,
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getComprehensiveQuizReport:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi tạo báo cáo tổng quan',
            details: error.message
        });
    }
};

/**
 * API: Lấy phân tích chi tiết theo nhóm học sinh
 * GET /api/teacher-analytics/quiz/:quizId/student-groups/:groupType
 */
const getStudentGroupAnalysis = async (req, res) => {
    try {
        const { quizId, groupType } = req.params;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Validate groupType
        const validGroups = ['excellent', 'good', 'average', 'weak'];
        if (!validGroups.includes(groupType)) {
            return res.status(400).json({
                error: 'Group type không hợp lệ',
                valid_types: validGroups
            });
        }

        // Lấy thông tin quiz
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy kết quả quiz
        const quizResults = await QuizResult.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'Student',
                    attributes: ['user_id', 'name', 'email']
                }
            ]
        });

        // Lấy lịch sử trả lời câu hỏi
        const questionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'User',
                    attributes: ['user_id', 'name', 'email']
                },
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id']
                }
            ]
        });

        // Phân nhóm học sinh
        const studentGroups = categorizeStudentsByPerformance(quizResults, questionHistory);
        const targetGroup = studentGroups[groupType];

        if (!targetGroup || targetGroup.students.length === 0) {
            return res.status(404).json({
                error: `Không có học sinh nào trong nhóm ${groupType}`
            });
        }

        // Phân tích chi tiết cho nhóm này
        const groupUserIds = targetGroup.students.map(s => s.user_id);
        const groupQuestionHistory = questionHistory.filter(h => groupUserIds.includes(h.user_id));

        // Phân tích LO cho nhóm
        const groupLOAnalysis = analyzeLOPerformance(quiz.Questions, groupQuestionHistory);

        // Phân tích Level cho nhóm
        const groupLevelAnalysis = analyzeLevelPerformance(quiz.Questions, groupQuestionHistory);

        // Tính toán thống kê nhóm
        const groupStats = {
            group_name: groupType,
            student_count: targetGroup.students.length,
            score_range: {
                min: Math.min(...targetGroup.students.map(s => s.score)),
                max: Math.max(...targetGroup.students.map(s => s.score)),
                average: targetGroup.students.reduce((sum, s) => sum + s.score, 0) / targetGroup.students.length
            },
            completion_stats: {
                total_questions: quiz.Questions.length,
                average_correct: targetGroup.students.reduce((sum, s) => sum + s.correct_answers, 0) / targetGroup.students.length,
                average_time_per_question: targetGroup.students.reduce((sum, s) => sum + s.average_time_per_question, 0) / targetGroup.students.length
            }
        };

        // Tạo recommendations cụ thể cho nhóm
        const groupRecommendations = [];

        if (groupType === 'weak') {
            groupRecommendations.push({
                type: 'immediate_action',
                suggestion: 'Tổ chức buổi ôn tập riêng cho nhóm này',
                priority: 'high'
            });
            groupRecommendations.push({
                type: 'teaching_method',
                suggestion: 'Sử dụng phương pháp giảng dạy trực quan và thực hành nhiều hơn',
                priority: 'high'
            });
        } else if (groupType === 'excellent') {
            groupRecommendations.push({
                type: 'challenge',
                suggestion: 'Đưa ra các bài tập nâng cao và dự án thực tế',
                priority: 'medium'
            });
            groupRecommendations.push({
                type: 'mentoring',
                suggestion: 'Khuyến khích làm mentor cho các nhóm khác',
                priority: 'medium'
            });
        }

        return res.status(200).json({
            success: true,
            data: {
                quiz_info: {
                    quiz_id: quiz.quiz_id,
                    name: quiz.name,
                    total_questions: quiz.Questions.length
                },
                group_overview: {
                    ...groupStats,
                    insights: targetGroup.insights,
                    recommendations: groupRecommendations
                },
                students: targetGroup.students,
                learning_outcome_analysis: groupLOAnalysis,
                difficulty_level_analysis: groupLevelAnalysis,
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getStudentGroupAnalysis:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi phân tích nhóm học sinh',
            details: error.message
        });
    }
};

/**
 * API: So sánh performance giữa các quiz
 * GET /api/teacher-analytics/quiz-comparison
 */
const getQuizComparison = async (req, res) => {
    try {
        const { quiz_ids, subject_id } = req.query;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        let quizIds = [];

        if (quiz_ids) {
            // So sánh các quiz cụ thể
            quizIds = quiz_ids.split(',').map(id => parseInt(id));
        } else if (subject_id) {
            // Lấy tất cả quiz của subject
            const quizzes = await Quiz.findAll({
                where: { subject_id: parseInt(subject_id) },
                attributes: ['quiz_id'],
                limit: 10 // Giới hạn để tránh quá tải
            });
            quizIds = quizzes.map(q => q.quiz_id);
        } else {
            return res.status(400).json({
                error: 'Cần cung cấp quiz_ids hoặc subject_id'
            });
        }

        if (quizIds.length < 2) {
            return res.status(400).json({
                error: 'Cần ít nhất 2 quiz để so sánh'
            });
        }

        // Lấy thông tin và kết quả của tất cả quiz
        const quizComparisons = [];

        for (const quizId of quizIds) {
            const quiz = await Quiz.findByPk(quizId, {
                include: [
                    {
                        model: Question,
                        as: 'Questions',
                        through: { attributes: [] },
                        attributes: ['question_id', 'level_id', 'lo_id']
                    }
                ]
            });

            if (!quiz) continue;

            const quizResults = await QuizResult.findAll({
                where: { quiz_id: quizId }
            });

            const questionHistory = await UserQuestionHistory.findAll({
                where: { quiz_id: quizId }
            });

            // Tính toán metrics
            const totalParticipants = quizResults.length;
            const completedParticipants = quizResults.filter(r => r.status === 'completed').length;
            const averageScore = totalParticipants > 0 ?
                quizResults.reduce((acc, curr) => acc + curr.score, 0) / totalParticipants : 0;

            const totalAttempts = questionHistory.length;
            const correctAttempts = questionHistory.filter(h => h.is_correct).length;
            const overallAccuracy = totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0;

            quizComparisons.push({
                quiz_id: quiz.quiz_id,
                quiz_name: quiz.name,
                total_questions: quiz.Questions.length,
                total_participants: totalParticipants,
                completion_rate: totalParticipants > 0 ? (completedParticipants / totalParticipants) * 100 : 0,
                average_score: parseFloat(averageScore.toFixed(2)),
                overall_accuracy: parseFloat(overallAccuracy.toFixed(2)),
                difficulty_distribution: {
                    easy: quiz.Questions.filter(q => q.Level?.name?.toLowerCase().includes('easy')).length,
                    medium: quiz.Questions.filter(q => q.Level?.name?.toLowerCase().includes('medium')).length,
                    hard: quiz.Questions.filter(q => q.Level?.name?.toLowerCase().includes('hard')).length
                }
            });
        }

        // Tạo insights so sánh
        const comparisonInsights = [];

        const avgScores = quizComparisons.map(q => q.average_score);
        const bestQuiz = quizComparisons.find(q => q.average_score === Math.max(...avgScores));
        const worstQuiz = quizComparisons.find(q => q.average_score === Math.min(...avgScores));

        if (bestQuiz && worstQuiz && bestQuiz.quiz_id !== worstQuiz.quiz_id) {
            comparisonInsights.push({
                type: 'performance_comparison',
                message: `Quiz "${bestQuiz.quiz_name}" có điểm trung bình cao nhất (${bestQuiz.average_score}), quiz "${worstQuiz.quiz_name}" thấp nhất (${worstQuiz.average_score})`
            });
        }

        return res.status(200).json({
            success: true,
            data: {
                quiz_comparisons: quizComparisons,
                comparison_insights: comparisonInsights,
                summary: {
                    total_quizzes_compared: quizComparisons.length,
                    average_score_across_quizzes: parseFloat((avgScores.reduce((a, b) => a + b, 0) / avgScores.length).toFixed(2)),
                    best_performing_quiz: bestQuiz,
                    worst_performing_quiz: worstQuiz
                },
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getQuizComparison:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi so sánh quiz',
            details: error.message
        });
    }
};

/**
 * API: Lấy phân tích chi tiết cá nhân học sinh với insights
 * GET /api/teacher-analytics/quiz/:quizId/student/:userId/detailed-analysis
 */
const getStudentDetailedAnalysis = async (req, res) => {
    try {
        const { quizId, userId } = req.params;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Lấy thông tin quiz
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id', 'explanation'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        },
                        {
                            model: Answer,
                            as: 'Answers',
                            attributes: ['answer_id', 'answer_text', 'iscorrect']
                        }
                    ]
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy thông tin học sinh
        const student = await User.findByPk(userId, {
            attributes: ['user_id', 'name', 'email']
        });

        if (!student) {
            return res.status(404).json({ error: 'Không tìm thấy học sinh' });
        }

        // Lấy kết quả quiz của học sinh
        const quizResult = await QuizResult.findOne({
            where: { quiz_id: quizId, user_id: userId }
        });

        if (!quizResult) {
            return res.status(404).json({ error: 'Học sinh chưa làm quiz này' });
        }

        // Lấy lịch sử trả lời câu hỏi của học sinh
        const questionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId, user_id: userId },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id', 'explanation'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        },
                        {
                            model: Answer,
                            as: 'Answers',
                            attributes: ['answer_id', 'answer_text', 'iscorrect']
                        }
                    ]
                }
            ],
            order: [['attempt_date', 'ASC']]
        });

        // Phân tích chi tiết từng câu hỏi
        const questionAnalysis = questionHistory.map(history => {
            const question = history.Question;
            const correctAnswer = question.Answers?.find(a => a.iscorrect);
            const selectedAnswer = question.Answers?.find(a => a.answer_id === history.selected_answer);

            let insights = [];
            let recommendations = [];

            if (!history.is_correct) {
                insights.push('Câu trả lời sai');
                if (question.explanation) {
                    recommendations.push(`Xem lại giải thích: ${question.explanation}`);
                }
                recommendations.push(`Ôn tập lại LO: ${question.LO?.name}`);

                if (history.time_spent > 120) {
                    insights.push('Mất nhiều thời gian suy nghĩ - có thể chưa nắm vững kiến thức');
                    recommendations.push('Cần củng cố kiến thức cơ bản');
                }
            } else {
                insights.push('Câu trả lời đúng');
                if (history.time_spent < 30) {
                    insights.push('Trả lời nhanh và chính xác - nắm vững kiến thức');
                }
            }

            return {
                question_id: question.question_id,
                question_text: question.question_text,
                level: question.Level?.name,
                lo: question.LO?.name,
                selected_answer_text: selectedAnswer?.answer_text || 'Không có đáp án',
                correct_answer_text: correctAnswer?.answer_text || 'Không xác định',
                is_correct: history.is_correct,
                time_spent: history.time_spent,
                attempt_date: history.attempt_date,
                insights,
                recommendations
            };
        });

        // Phân tích theo LO
        const loPerformance = {};
        questionHistory.forEach(history => {
            const loId = history.Question.lo_id;
            const loName = history.Question.LO?.name || 'Unknown LO';

            if (!loPerformance[loId]) {
                loPerformance[loId] = {
                    lo_name: loName,
                    total_questions: 0,
                    correct_answers: 0,
                    total_time: 0
                };
            }

            loPerformance[loId].total_questions++;
            if (history.is_correct) loPerformance[loId].correct_answers++;
            loPerformance[loId].total_time += history.time_spent || 0;
        });

        const loAnalysis = Object.values(loPerformance).map(lo => {
            const accuracy = (lo.correct_answers / lo.total_questions) * 100;
            const avgTime = lo.total_time / lo.total_questions;

            let performance_level = 'good';
            let insights = [];
            let recommendations = [];

            if (accuracy < 50) {
                performance_level = 'weak';
                insights.push(`LO yếu - chỉ đúng ${accuracy.toFixed(1)}%`);
                recommendations.push(`Cần ôn tập kỹ LO: ${lo.lo_name}`);
            } else if (accuracy < 80) {
                performance_level = 'average';
                insights.push(`LO trung bình - đúng ${accuracy.toFixed(1)}%`);
                recommendations.push(`Luyện tập thêm LO: ${lo.lo_name}`);
            } else {
                insights.push(`LO tốt - đúng ${accuracy.toFixed(1)}%`);
            }

            return {
                ...lo,
                accuracy: parseFloat(accuracy.toFixed(2)),
                average_time: parseFloat(avgTime.toFixed(2)),
                performance_level,
                insights,
                recommendations
            };
        });

        // Tạo tổng kết và recommendations cho học sinh
        const overallInsights = [];
        const overallRecommendations = [];

        const totalQuestions = questionHistory.length;
        const correctAnswers = questionHistory.filter(h => h.is_correct).length;
        const accuracy = (correctAnswers / totalQuestions) * 100;
        const avgTime = questionHistory.reduce((sum, h) => sum + (h.time_spent || 0), 0) / totalQuestions;

        if (accuracy < 50) {
            overallInsights.push('Kết quả chưa tốt - cần cải thiện nhiều');
            overallRecommendations.push('Cần hỗ trợ đặc biệt và ôn tập từ cơ bản');
        } else if (accuracy < 70) {
            overallInsights.push('Kết quả trung bình - có thể cải thiện');
            overallRecommendations.push('Tăng cường luyện tập và ôn tập');
        } else if (accuracy < 85) {
            overallInsights.push('Kết quả tốt - cần duy trì');
            overallRecommendations.push('Tiếp tục duy trì và phát triển');
        } else {
            overallInsights.push('Kết quả xuất sắc');
            overallRecommendations.push('Có thể thử thách với bài tập nâng cao');
        }

        if (avgTime > 120) {
            overallInsights.push('Thời gian làm bài chậm - cần rèn luyện tốc độ');
            overallRecommendations.push('Luyện tập để tăng tốc độ làm bài');
        }

        return res.status(200).json({
            success: true,
            data: {
                quiz_info: {
                    quiz_id: quiz.quiz_id,
                    name: quiz.name,
                    total_questions: quiz.Questions.length
                },
                student_info: {
                    user_id: student.user_id,
                    name: student.name,
                    email: student.email
                },
                performance_summary: {
                    score: quizResult.score,
                    total_questions: totalQuestions,
                    correct_answers: correctAnswers,
                    accuracy: parseFloat(accuracy.toFixed(2)),
                    average_time_per_question: parseFloat(avgTime.toFixed(2)),
                    completion_time: quizResult.completion_time,
                    status: quizResult.status
                },
                overall_insights: {
                    insights: overallInsights,
                    recommendations: overallRecommendations
                },
                learning_outcome_analysis: loAnalysis,
                question_by_question_analysis: questionAnalysis,
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getStudentDetailedAnalysis:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi phân tích chi tiết học sinh',
            details: error.message
        });
    }
};

/**
 * API: Lấy insights và recommendations tổng hợp cho giảng viên
 * GET /api/teacher-analytics/quiz/:quizId/teaching-insights
 */
const getTeachingInsights = async (req, res) => {
    try {
        const quizId = req.params.quizId;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Lấy thông tin quiz
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                },
                {
                    model: Subject,
                    attributes: ['subject_id', 'name']
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy dữ liệu cần thiết
        const quizResults = await QuizResult.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'Student',
                    attributes: ['user_id', 'name', 'email']
                }
            ]
        });

        const questionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId }
        });

        // Phân tích cơ bản
        const totalParticipants = quizResults.length;
        const completedParticipants = quizResults.filter(r => r.status === 'completed').length;
        const averageScore = totalParticipants > 0 ?
            quizResults.reduce((acc, curr) => acc + curr.score, 0) / totalParticipants : 0;

        // Phân tích chi tiết
        const loAnalysis = analyzeLOPerformance(quiz.Questions, questionHistory);
        const levelAnalysis = analyzeLevelPerformance(quiz.Questions, questionHistory);
        const studentGroups = categorizeStudentsByPerformance(quizResults, questionHistory);

        // Tạo insights nâng cao
        const teachingInsights = {
            // Insights về curriculum
            curriculum_insights: [],

            // Insights về phương pháp giảng dạy
            teaching_method_insights: [],

            // Insights về học sinh
            student_insights: [],

            // Recommendations hành động
            action_recommendations: [],

            // Priorities
            priority_actions: []
        };

        // Phân tích curriculum
        const weakLOs = loAnalysis.filter(lo => lo.performance_level === 'weak');
        const strongLOs = loAnalysis.filter(lo => lo.performance_level === 'good');

        if (weakLOs.length > 0) {
            teachingInsights.curriculum_insights.push({
                type: 'weakness',
                message: `${weakLOs.length} LO có hiệu suất thấp: ${weakLOs.map(lo => lo.lo_name).join(', ')}`,
                impact: 'high',
                affected_students: weakLOs.reduce((sum, lo) => sum + lo.students_attempted, 0)
            });

            teachingInsights.action_recommendations.push({
                category: 'curriculum_revision',
                action: 'Xem xét điều chỉnh nội dung giảng dạy cho các LO yếu',
                priority: 'high',
                timeline: 'immediate',
                affected_los: weakLOs.map(lo => lo.lo_name)
            });
        }

        if (strongLOs.length > weakLOs.length) {
            teachingInsights.curriculum_insights.push({
                type: 'strength',
                message: `${strongLOs.length} LO có hiệu suất tốt - phương pháp giảng dạy hiệu quả`,
                impact: 'positive'
            });
        }

        // Phân tích phương pháp giảng dạy
        const avgTimePerQuestion = questionHistory.length > 0 ?
            questionHistory.reduce((sum, h) => sum + (h.time_spent || 0), 0) / questionHistory.length : 0;

        if (avgTimePerQuestion > 120) {
            teachingInsights.teaching_method_insights.push({
                type: 'time_concern',
                message: 'Học sinh mất nhiều thời gian trả lời (trung bình ' + avgTimePerQuestion.toFixed(1) + 's) - có thể câu hỏi khó hiểu',
                suggestion: 'Xem xét đơn giản hóa ngôn ngữ câu hỏi hoặc bổ sung ví dụ minh họa'
            });

            teachingInsights.action_recommendations.push({
                category: 'question_improvement',
                action: 'Rà soát và cải thiện độ rõ ràng của câu hỏi',
                priority: 'medium',
                timeline: 'next_quiz'
            });
        }

        // Phân tích học sinh
        const weakStudentRatio = studentGroups.weak.students.length / totalParticipants;
        const excellentStudentRatio = studentGroups.excellent.students.length / totalParticipants;

        if (weakStudentRatio > 0.3) {
            teachingInsights.student_insights.push({
                type: 'concern',
                message: `${(weakStudentRatio * 100).toFixed(1)}% học sinh có kết quả yếu - cần can thiệp`,
                affected_count: studentGroups.weak.students.length
            });

            teachingInsights.priority_actions.push({
                action: 'Tổ chức lớp phụ đạo cho nhóm học sinh yếu',
                urgency: 'high',
                estimated_time: '2-3 tuần',
                expected_outcome: 'Cải thiện hiểu biết cơ bản'
            });
        }

        if (excellentStudentRatio > 0.4) {
            teachingInsights.student_insights.push({
                type: 'opportunity',
                message: `${(excellentStudentRatio * 100).toFixed(1)}% học sinh xuất sắc - có thể tận dụng làm peer tutor`,
                opportunity_count: studentGroups.excellent.students.length
            });

            teachingInsights.action_recommendations.push({
                category: 'peer_learning',
                action: 'Tổ chức chương trình peer tutoring',
                priority: 'medium',
                timeline: 'next_month',
                benefits: ['Tăng cường học tập nhóm', 'Phát triển kỹ năng lãnh đạo cho học sinh giỏi']
            });
        }

        // Tạo summary insights
        const summaryInsights = {
            overall_assessment: averageScore >= 70 ? 'positive' : averageScore >= 50 ? 'mixed' : 'concerning',
            key_strengths: [],
            main_challenges: [],
            immediate_actions_needed: teachingInsights.priority_actions.length,
            long_term_improvements: teachingInsights.action_recommendations.filter(r => r.timeline !== 'immediate').length
        };

        if (averageScore >= 70) {
            summaryInsights.key_strengths.push('Điểm trung bình tốt');
        }
        if (completedParticipants / totalParticipants >= 0.8) {
            summaryInsights.key_strengths.push('Tỷ lệ hoàn thành cao');
        }
        if (strongLOs.length > weakLOs.length) {
            summaryInsights.key_strengths.push('Đa số LO được nắm vững');
        }

        if (weakLOs.length > 0) {
            summaryInsights.main_challenges.push(`${weakLOs.length} LO cần cải thiện`);
        }
        if (weakStudentRatio > 0.2) {
            summaryInsights.main_challenges.push('Tỷ lệ học sinh yếu cao');
        }

        return res.status(200).json({
            success: true,
            data: {
                quiz_info: {
                    quiz_id: quiz.quiz_id,
                    name: quiz.name,
                    subject_name: quiz.Subject?.name
                },
                summary_insights: summaryInsights,
                detailed_insights: teachingInsights,
                metrics: {
                    total_participants: totalParticipants,
                    average_score: parseFloat(averageScore.toFixed(2)),
                    completion_rate: parseFloat(((completedParticipants / totalParticipants) * 100).toFixed(2)),
                    weak_los_count: weakLOs.length,
                    strong_los_count: strongLOs.length,
                    weak_students_count: studentGroups.weak.students.length,
                    excellent_students_count: studentGroups.excellent.students.length
                },
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getTeachingInsights:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi tạo insights giảng dạy',
            details: error.message
        });
    }
};

/**
 * API: Lấy benchmark và so sánh với historical data
 * GET /api/teacher-analytics/quiz/:quizId/benchmark
 */
const getQuizBenchmark = async (req, res) => {
    try {
        const quizId = req.params.quizId;
        const { compare_with_subject = true, compare_with_teacher = true } = req.query;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher'].includes(req.roleName)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Lấy thông tin quiz hiện tại
        const currentQuiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Subject,
                    attributes: ['subject_id', 'name']
                }
            ]
        });

        if (!currentQuiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy kết quả quiz hiện tại
        const currentResults = await QuizResult.findAll({
            where: { quiz_id: quizId }
        });

        const currentQuestionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId }
        });

        // Tính metrics cho quiz hiện tại
        const currentMetrics = await calculateQuizMetrics(quizId, currentResults, currentQuestionHistory);

        // Helper function để tính metrics chính xác
        async function calculateQuizMetrics(quizId, results, questionHistory) {
            if (results.length === 0) {
                return {
                    total_participants: 0,
                    average_score: 0,
                    completion_rate: 0,
                    pass_rate: 0,
                    excellence_rate: 0,
                    overall_accuracy: 0
                };
            }

            // Tính điểm phần trăm cho từng học sinh
            const studentScores = [];
            for (const result of results) {
                const studentHistory = questionHistory.filter(h => h.user_id === result.user_id);
                if (studentHistory.length > 0) {
                    const correctAnswers = studentHistory.filter(h => h.is_correct).length;
                    const totalQuestions = studentHistory.length;
                    const percentageScore = (correctAnswers / totalQuestions) * 100;
                    studentScores.push({
                        user_id: result.user_id,
                        raw_score: result.score,
                        percentage_score: percentageScore,
                        status: result.status
                    });
                }
            }

            const avgPercentage = studentScores.length > 0 ?
                studentScores.reduce((sum, s) => sum + s.percentage_score, 0) / studentScores.length : 0;

            return {
                total_participants: results.length,
                average_score: results.reduce((sum, r) => sum + r.score, 0) / results.length,
                percentage_average: avgPercentage,
                completion_rate: (results.filter(r => r.status === 'completed').length / results.length) * 100,
                pass_rate: studentScores.length > 0 ?
                    (studentScores.filter(s => s.percentage_score >= 50).length / studentScores.length) * 100 : 0,
                excellence_rate: studentScores.length > 0 ?
                    (studentScores.filter(s => s.percentage_score >= 85).length / studentScores.length) * 100 : 0,
                overall_accuracy: avgPercentage
            };
        }

        const benchmarkData = {
            current_quiz: {
                quiz_id: currentQuiz.quiz_id,
                name: currentQuiz.name,
                subject_name: currentQuiz.Subject?.name,
                metrics: currentMetrics
            },
            comparisons: {},
            insights: [],
            recommendations: []
        };

        // So sánh với các quiz khác trong cùng subject
        if (compare_with_subject === 'true' && currentQuiz.subject_id) {
            // Thử tìm với nhiều status khác nhau, không chỉ 'finished'
            const subjectQuizzes = await Quiz.findAll({
                where: {
                    subject_id: currentQuiz.subject_id,
                    quiz_id: { [Op.ne]: quizId },
                    status: { [Op.in]: ['finished', 'ended', 'active'] } // Mở rộng status
                },
                limit: 10,
                order: [['update_time', 'DESC']]
            });

            const subjectMetrics = [];

            for (const quiz of subjectQuizzes) {
                const results = await QuizResult.findAll({
                    where: { quiz_id: quiz.quiz_id }
                });

                if (results.length > 0) {
                    // Lấy question history cho quiz này
                    const quizQuestionHistory = await UserQuestionHistory.findAll({
                        where: { quiz_id: quiz.quiz_id }
                    });

                    // Tính metrics chính xác cho quiz này
                    const quizMetrics = await calculateQuizMetrics(quiz.quiz_id, results, quizQuestionHistory);

                    subjectMetrics.push({
                        quiz_id: quiz.quiz_id,
                        quiz_name: quiz.name,
                        average_score: quizMetrics.average_score,
                        percentage_average: quizMetrics.percentage_average,
                        completion_rate: quizMetrics.completion_rate,
                        pass_rate: quizMetrics.pass_rate,
                        excellence_rate: quizMetrics.excellence_rate
                    });
                }
            }

            if (subjectMetrics.length > 0) {
                const subjectAverage = {
                    average_score: subjectMetrics.reduce((sum, m) => sum + m.average_score, 0) / subjectMetrics.length,
                    percentage_average: subjectMetrics.reduce((sum, m) => sum + m.percentage_average, 0) / subjectMetrics.length,
                    completion_rate: subjectMetrics.reduce((sum, m) => sum + m.completion_rate, 0) / subjectMetrics.length,
                    pass_rate: subjectMetrics.reduce((sum, m) => sum + m.pass_rate, 0) / subjectMetrics.length,
                    excellence_rate: subjectMetrics.reduce((sum, m) => sum + m.excellence_rate, 0) / subjectMetrics.length
                };

                benchmarkData.comparisons.subject_benchmark = {
                    comparison_base: `${subjectMetrics.length} quiz khác trong subject ${currentQuiz.Subject?.name}`,
                    total_compared_quizzes: subjectMetrics.length,
                    subject_average: subjectAverage,
                    current_vs_average: {
                        score_difference: parseFloat((currentMetrics.average_score - subjectAverage.average_score).toFixed(2)),
                        percentage_difference: parseFloat((currentMetrics.percentage_average - subjectAverage.percentage_average).toFixed(2)),
                        completion_difference: parseFloat((currentMetrics.completion_rate - subjectAverage.completion_rate).toFixed(2)),
                        pass_rate_difference: parseFloat((currentMetrics.pass_rate - subjectAverage.pass_rate).toFixed(2)),
                        excellence_difference: parseFloat((currentMetrics.excellence_rate - subjectAverage.excellence_rate).toFixed(2))
                    }
                };

                // Tạo insights dựa trên so sánh
                const percentageDiff = currentMetrics.percentage_average - subjectAverage.percentage_average;
                if (percentageDiff > 10) {
                    benchmarkData.insights.push({
                        type: 'positive',
                        category: 'performance',
                        message: `Quiz này có điểm trung bình cao hơn ${percentageDiff.toFixed(1)}% so với trung bình subject`,
                        impact: 'high'
                    });
                } else if (percentageDiff < -10) {
                    benchmarkData.insights.push({
                        type: 'negative',
                        category: 'performance',
                        message: `Quiz này có điểm trung bình thấp hơn ${Math.abs(percentageDiff).toFixed(1)}% so với trung bình subject`,
                        impact: 'high'
                    });

                    benchmarkData.recommendations.push({
                        category: 'improvement',
                        suggestion: 'Xem xét điều chỉnh độ khó hoặc phương pháp giảng dạy',
                        priority: 'high',
                        rationale: 'Điểm số thấp hơn đáng kể so với các quiz khác trong subject'
                    });
                }

                const passRateDiff = currentMetrics.pass_rate - subjectAverage.pass_rate;
                if (passRateDiff > 15) {
                    benchmarkData.insights.push({
                        type: 'positive',
                        category: 'participation',
                        message: `Tỷ lệ đậu cao hơn ${passRateDiff.toFixed(1)}% so với trung bình subject`,
                        impact: 'medium'
                    });
                } else if (passRateDiff < -15) {
                    benchmarkData.insights.push({
                        type: 'warning',
                        category: 'pass_rate',
                        message: `Tỷ lệ đậu thấp hơn ${Math.abs(passRateDiff).toFixed(1)}% so với trung bình subject`,
                        impact: 'high'
                    });

                    benchmarkData.recommendations.push({
                        category: 'student_support',
                        suggestion: 'Tăng cường hỗ trợ học sinh và xem xét giảm độ khó',
                        priority: 'high',
                        rationale: 'Tỷ lệ đậu thấp có thể ảnh hưởng đến động lực học tập'
                    });
                }

                const completionDiff = currentMetrics.completion_rate - subjectAverage.completion_rate;
                if (completionDiff > 10) {
                    benchmarkData.insights.push({
                        type: 'positive',
                        category: 'participation',
                        message: `Tỷ lệ hoàn thành cao hơn ${completionDiff.toFixed(1)}% so với trung bình subject`,
                        impact: 'medium'
                    });
                } else if (completionDiff < -10) {
                    benchmarkData.insights.push({
                        type: 'warning',
                        category: 'participation',
                        message: `Tỷ lệ hoàn thành thấp hơn ${Math.abs(completionDiff).toFixed(1)}% so với trung bình subject`,
                        impact: 'medium'
                    });
                }
            }
        }

        // Tạo performance ranking
        let performanceRanking = null;
        if (benchmarkData.comparisons.subject_benchmark && subjectMetrics.length > 0) {
            const allQuizzes = [
                {
                    name: currentQuiz.name,
                    score: currentMetrics.percentage_average,
                    is_current: true
                },
                ...subjectMetrics.map(m => ({
                    name: m.quiz_name,
                    score: m.percentage_average,
                    is_current: false
                }))
            ];

            allQuizzes.sort((a, b) => b.score - a.score);
            const currentRank = allQuizzes.findIndex(q => q.is_current) + 1;

            performanceRanking = {
                subject_rank: currentRank,
                total_subject_quizzes: allQuizzes.length,
                subject_percentile: parseFloat(((allQuizzes.length - currentRank + 1) / allQuizzes.length * 100).toFixed(1)),
                ranking_insights: currentRank <= 3 ? 'Top performer trong subject' :
                    currentRank <= allQuizzes.length * 0.5 ? 'Trên trung bình subject' : 'Dưới trung bình subject'
            };
        } else {
            // Nếu không có dữ liệu so sánh, tạo ranking cơ bản
            performanceRanking = {
                subject_rank: 1,
                total_subject_quizzes: 1,
                subject_percentile: 100.0,
                ranking_insights: 'Chưa có dữ liệu so sánh trong subject'
            };

            // Tạo insights dựa trên performance tuyệt đối khi không có so sánh
            console.log('No comparison data available, generating absolute performance insights');

            // Insights về điểm số
            if (currentMetrics.percentage_average >= 90) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'performance',
                    message: `Kết quả xuất sắc với điểm trung bình ${currentMetrics.percentage_average.toFixed(1)}%`,
                    impact: 'high'
                });
            } else if (currentMetrics.percentage_average >= 75) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'performance',
                    message: `Kết quả tốt với điểm trung bình ${currentMetrics.percentage_average.toFixed(1)}%`,
                    impact: 'medium'
                });
            } else if (currentMetrics.percentage_average >= 50) {
                benchmarkData.insights.push({
                    type: 'neutral',
                    category: 'performance',
                    message: `Kết quả trung bình với điểm ${currentMetrics.percentage_average.toFixed(1)}%`,
                    impact: 'medium'
                });
            } else {
                benchmarkData.insights.push({
                    type: 'negative',
                    category: 'performance',
                    message: `Kết quả cần cải thiện với điểm ${currentMetrics.percentage_average.toFixed(1)}%`,
                    impact: 'high'
                });
            }

            // Insights về tỷ lệ hoàn thành
            if (currentMetrics.completion_rate === 100) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'participation',
                    message: 'Tỷ lệ hoàn thành 100% - rất tốt',
                    impact: 'medium'
                });
            } else if (currentMetrics.completion_rate >= 80) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'participation',
                    message: `Tỷ lệ hoàn thành cao (${currentMetrics.completion_rate.toFixed(1)}%)`,
                    impact: 'low'
                });
            } else if (currentMetrics.completion_rate < 60) {
                benchmarkData.insights.push({
                    type: 'warning',
                    category: 'participation',
                    message: `Tỷ lệ hoàn thành thấp (${currentMetrics.completion_rate.toFixed(1)}%)`,
                    impact: 'medium'
                });
            }

            // Insights về tỷ lệ đậu
            if (currentMetrics.pass_rate === 100) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'pass_rate',
                    message: 'Tỷ lệ đậu 100% - xuất sắc',
                    impact: 'high'
                });
            } else if (currentMetrics.pass_rate >= 80) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'pass_rate',
                    message: `Tỷ lệ đậu cao (${currentMetrics.pass_rate.toFixed(1)}%)`,
                    impact: 'medium'
                });
            } else if (currentMetrics.pass_rate < 50) {
                benchmarkData.insights.push({
                    type: 'warning',
                    category: 'pass_rate',
                    message: `Tỷ lệ đậu thấp (${currentMetrics.pass_rate.toFixed(1)}%)`,
                    impact: 'high'
                });
            }

            // Insights về tỷ lệ xuất sắc
            if (currentMetrics.excellence_rate >= 50) {
                benchmarkData.insights.push({
                    type: 'positive',
                    category: 'excellence',
                    message: `Tỷ lệ xuất sắc cao (${currentMetrics.excellence_rate.toFixed(1)}%)`,
                    impact: 'medium'
                });
            } else if (currentMetrics.excellence_rate === 0) {
                benchmarkData.insights.push({
                    type: 'neutral',
                    category: 'excellence',
                    message: 'Chưa có học sinh đạt mức xuất sắc',
                    impact: 'low'
                });
            }
        }

        benchmarkData.performance_ranking = performanceRanking;

        // Tạo improvement suggestions dựa trên performance tổng thể
        if (currentMetrics.percentage_average < 50) {
            benchmarkData.recommendations.push({
                category: 'urgent_improvement',
                suggestion: 'Điểm trung bình rất thấp - cần xem xét lại toàn bộ phương pháp giảng dạy',
                priority: 'critical',
                timeline: 'immediate',
                rationale: `Điểm trung bình chỉ ${currentMetrics.percentage_average.toFixed(1)}% - dưới mức đạt`
            });
        } else if (currentMetrics.percentage_average < 70) {
            benchmarkData.recommendations.push({
                category: 'improvement',
                suggestion: 'Điểm trung bình cần cải thiện - xem xét điều chỉnh phương pháp giảng dạy',
                priority: 'high',
                timeline: 'short_term',
                rationale: `Điểm trung bình ${currentMetrics.percentage_average.toFixed(1)}% - có thể cải thiện`
            });
        }

        if (currentMetrics.completion_rate < 70) {
            benchmarkData.recommendations.push({
                category: 'engagement',
                suggestion: 'Tỷ lệ hoàn thành thấp - cần tăng cường động lực học tập',
                priority: 'high',
                timeline: 'next_quiz',
                rationale: `Chỉ ${currentMetrics.completion_rate.toFixed(1)}% học sinh hoàn thành quiz`
            });
        }

        if (currentMetrics.pass_rate < 50) {
            benchmarkData.recommendations.push({
                category: 'assessment_adjustment',
                suggestion: 'Tỷ lệ đậu thấp - cần xem xét giảm độ khó hoặc tăng cường hỗ trợ',
                priority: 'high',
                timeline: 'immediate',
                rationale: `Chỉ ${currentMetrics.pass_rate.toFixed(1)}% học sinh đạt điểm đậu`
            });
        }

        // Thêm recommendations tích cực nếu performance tốt
        if (currentMetrics.percentage_average >= 85 && currentMetrics.pass_rate >= 80) {
            benchmarkData.recommendations.push({
                category: 'maintenance',
                suggestion: 'Duy trì phương pháp giảng dạy hiện tại - kết quả rất tốt',
                priority: 'medium',
                timeline: 'long_term',
                rationale: 'Quiz đạt kết quả xuất sắc'
            });
        }

        // Đảm bảo luôn có ít nhất một recommendation
        if (benchmarkData.recommendations.length === 0) {
            if (currentMetrics.percentage_average >= 80) {
                benchmarkData.recommendations.push({
                    category: 'maintenance',
                    suggestion: 'Tiếp tục duy trì chất lượng giảng dạy tốt',
                    priority: 'low',
                    timeline: 'long_term',
                    rationale: 'Kết quả quiz đạt mức tốt'
                });
            } else if (currentMetrics.percentage_average >= 60) {
                benchmarkData.recommendations.push({
                    category: 'improvement',
                    suggestion: 'Xem xét cải thiện phương pháp giảng dạy để nâng cao điểm số',
                    priority: 'medium',
                    timeline: 'short_term',
                    rationale: 'Còn tiềm năng cải thiện điểm số'
                });
            } else {
                benchmarkData.recommendations.push({
                    category: 'urgent_improvement',
                    suggestion: 'Cần xem xét lại toàn bộ phương pháp giảng dạy',
                    priority: 'high',
                    timeline: 'immediate',
                    rationale: 'Điểm số thấp cần cải thiện ngay'
                });
            }
        }

        return res.status(200).json({
            success: true,
            data: {
                ...benchmarkData,
                generated_at: new Date()
            }
        });

    } catch (error) {
        console.error('Error in getQuizBenchmark:', error);
        return res.status(500).json({
            success: false,
            error: 'Lỗi khi tạo benchmark',
            details: error.message
        });
    }
};

/**
 * API Debug: Kiểm tra dữ liệu quiz để debug
 * GET /api/teacher-analytics/debug/:quizId
 */
const debugQuizData = async (req, res) => {
    try {
        const quizId = req.params.quizId;

        console.log('=== DEBUG QUIZ DATA ===');
        console.log('Quiz ID:', quizId);

        // Kiểm tra quiz
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                        { model: LO, as: 'LO', attributes: ['lo_id', 'name'] }
                    ]
                },
                { model: Subject, attributes: ['subject_id', 'name'] }
            ]
        });

        console.log('Quiz found:', !!quiz);
        if (!quiz) {
            return res.status(404).json({ error: 'Quiz not found', quiz_id: quizId });
        }

        // Kiểm tra QuizResults
        const quizResults = await QuizResult.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: User,
                    as: 'Student',
                    attributes: ['user_id', 'name', 'email']
                }
            ]
        });

        console.log('QuizResults count:', quizResults.length);

        // Kiểm tra UserQuestionHistory
        const questionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId }
        });

        console.log('UserQuestionHistory count:', questionHistory.length);

        // Test phân nhóm
        const studentGroups = categorizeStudentsByPerformance(quizResults, questionHistory);

        console.log('Student groups:', {
            excellent: studentGroups.excellent.students.length,
            good: studentGroups.good.students.length,
            average: studentGroups.average.students.length,
            weak: studentGroups.weak.students.length
        });

        return res.status(200).json({
            success: true,
            debug_data: {
                quiz_info: {
                    quiz_id: quiz.quiz_id,
                    name: quiz.name,
                    subject_name: quiz.Subject?.name,
                    total_questions: quiz.Questions?.length || 0
                },
                data_counts: {
                    quiz_results: quizResults.length,
                    question_history: questionHistory.length
                },
                sample_quiz_results: quizResults.slice(0, 3).map(r => ({
                    user_id: r.user_id,
                    student_name: r.Student?.name,
                    score: r.score,
                    status: r.status,
                    completion_time: r.completion_time
                })),
                sample_question_history: questionHistory.slice(0, 3).map(h => ({
                    user_id: h.user_id,
                    question_id: h.question_id,
                    is_correct: h.is_correct,
                    time_spent: h.time_spent
                })),
                student_groups_count: {
                    excellent: studentGroups.excellent.students.length,
                    good: studentGroups.good.students.length,
                    average: studentGroups.average.students.length,
                    weak: studentGroups.weak.students.length
                },
                sample_students_by_group: {
                    excellent: studentGroups.excellent.students.slice(0, 2),
                    good: studentGroups.good.students.slice(0, 2),
                    average: studentGroups.average.students.slice(0, 2),
                    weak: studentGroups.weak.students.slice(0, 2)
                }
            }
        });

    } catch (error) {
        console.error('Debug error:', error);
        return res.status(500).json({
            success: false,
            error: 'Debug failed',
            details: error.message
        });
    }
};

module.exports = {
    analyzeLOPerformance,
    analyzeLevelPerformance,
    categorizeStudentsByPerformance,
    generateTeacherInsights,
    getComprehensiveQuizReport,
    getStudentGroupAnalysis,
    getQuizComparison,
    getStudentDetailedAnalysis,
    getTeachingInsights,
    getQuizBenchmark,
    debugQuizData
};

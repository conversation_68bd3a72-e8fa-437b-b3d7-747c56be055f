# Teacher Analytics API Documentation

## Tổng quan
Bộ API Teacher Analytics cung cấp các công cụ phân tích chuyên sâu cho giảng viên để đánh giá hiệu quả quiz và hiểu rõ tình hình học tập của học sinh.

## Base URL
```
/api/teacher-analytics
```

## Authentication
Tất cả API yêu cầu authentication token và chỉ dành cho role `admin` và `teacher`.

## API Endpoints

### 1. Comprehensive Quiz Report
**GET** `/quiz/:quizId/comprehensive-report`

Lấy báo cáo tổng quan chi tiết về quiz với phân tích điểm mạnh/yếu và insights.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz/{quizId}/comprehensive-report`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz cần phân tích

**Response Structure:**
```typescript
interface ComprehensiveQuizReportResponse {
  success: boolean;
  data: {
    quiz_info: {
      quiz_id: number;
      name: string;
      subject_name: string;
      total_questions: number;
    };
    overall_performance: {
      total_participants: number;
      average_score: number;
      completion_rate: number;
      pass_rate: number;
    };
    learning_outcome_analysis: Array<{
      lo_id: number;
      lo_name: string;
      total_questions: number;
      correct_answers: number;
      accuracy: number;
      performance_level: "excellent" | "good" | "average" | "weak";
      insights: string[];
      recommendations: string[];
    }>;
    level_analysis: Array<{
      level_id: number;
      level_name: string;
      total_questions: number;
      correct_answers: number;
      accuracy: number;
      performance_level: "excellent" | "good" | "average" | "weak";
      insights: string[];
      recommendations: string[];
    }>;
    student_groups: {
      excellent: { count: number; percentage: number };
      good: { count: number; percentage: number };
      average: { count: number; percentage: number };
      weak: { count: number; percentage: number };
    };
    teacher_insights: Array<{
      category: "strengths" | "weaknesses" | "opportunities" | "recommendations";
      message: string;
      priority: "high" | "medium" | "low" | "maintain";
      impact?: "high" | "medium" | "low";
    }>;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 122,
      "name": "Bài tập Thiết kế web",
      "subject_name": "Thiết kế web",
      "total_questions": 12
    },
    "overall_performance": {
      "total_participants": 2,
      "average_score": 8.33,
      "completion_rate": 100.0,
      "pass_rate": 100.0
    },
    "learning_outcome_analysis": [
      {
        "lo_id": 1,
        "lo_name": "Thiết kế giao diện",
        "total_questions": 8,
        "correct_answers": 32,
        "accuracy": 83.33,
        "performance_level": "good",
        "insights": ["LO tốt - học sinh nắm vững"],
        "recommendations": ["Duy trì chất lượng giảng dạy"]
      }
    ],
    "level_analysis": [
      {
        "level_id": 1,
        "level_name": "Nhận biết",
        "total_questions": 6,
        "correct_answers": 24,
        "accuracy": 91.67,
        "performance_level": "excellent",
        "insights": ["Level xuất sắc"],
        "recommendations": ["Tiếp tục phát triển"]
      }
    ],
    "student_groups": {
      "excellent": { "count": 1, "percentage": 50.0 },
      "good": { "count": 1, "percentage": 50.0 },
      "average": { "count": 0, "percentage": 0.0 },
      "weak": { "count": 0, "percentage": 0.0 }
    },
    "teacher_insights": [
      {
        "category": "strengths",
        "message": "Tỷ lệ hoàn thành cao (100%)",
        "priority": "maintain"
      },
      {
        "category": "opportunities",
        "message": "Có thể tăng độ khó để thách thức học sinh",
        "priority": "medium"
      }
    ]
  }
}
```

### 2. Student Group Analysis
**GET** `/quiz/:quizId/student-groups/:groupType`

Phân tích chi tiết theo nhóm học sinh (excellent, good, average, weak).

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz/{quizId}/student-groups/{groupType}`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz
  - `groupType` (string, required): Loại nhóm học sinh
    - Values: `excellent` | `good` | `average` | `weak`

**Response Structure:**
```typescript
interface StudentGroupAnalysisResponse {
  success: boolean;
  data: {
    group_overview: {
      group_name: string;
      student_count: number;
      score_range: {
        min: number;
        max: number;
        average: number;
      };
      percentage_range: {
        min: number;
        max: number;
        average: number;
      };
      insights: string[];
      recommendations: Array<{
        type: string;
        suggestion: string;
        priority: "high" | "medium" | "low";
      }>;
    };
    students: Array<{
      user_id: number;
      name: string;
      email: string;
      score: number;
      percentage_score: number;
      completion_time: number;
      average_time_per_question: number;
      total_questions_attempted: number;
      correct_answers: number;
    }>;
    learning_outcome_analysis: Array<{
      lo_id: number;
      lo_name: string;
      group_accuracy: number;
      performance_level: string;
      insights: string[];
      recommendations: string[];
    }>;
    difficulty_level_analysis: Array<{
      level_id: number;
      level_name: string;
      group_accuracy: number;
      performance_level: string;
      insights: string[];
      recommendations: string[];
    }>;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "group_overview": {
      "group_name": "excellent",
      "student_count": 1,
      "score_range": { "min": 9.17, "max": 9.17, "average": 9.17 },
      "percentage_range": { "min": 91.67, "max": 91.67, "average": 91.67 },
      "insights": ["Nhóm học sinh xuất sắc, có thể làm mentor cho nhóm khác"],
      "recommendations": [
        {
          "type": "enhancement",
          "suggestion": "Cân nhắc đưa ra thêm thách thức nâng cao",
          "priority": "medium"
        }
      ]
    },
    "students": [
      {
        "user_id": 120,
        "name": "Huỳnh Phan Vân Anh",
        "email": "<EMAIL>",
        "score": 9.17,
        "percentage_score": 91.67,
        "completion_time": 151,
        "average_time_per_question": 9757.92,
        "total_questions_attempted": 24,
        "correct_answers": 22
      }
    ],
    "learning_outcome_analysis": [
      {
        "lo_id": 1,
        "lo_name": "Thiết kế giao diện",
        "group_accuracy": 95.0,
        "performance_level": "excellent",
        "insights": ["Nhóm excellent nắm vững LO này"],
        "recommendations": ["Duy trì và phát triển thêm"]
      }
    ],
    "difficulty_level_analysis": [
      {
        "level_id": 1,
        "level_name": "Nhận biết",
        "group_accuracy": 100.0,
        "performance_level": "excellent",
        "insights": ["Hoàn hảo ở level cơ bản"],
        "recommendations": ["Thử thách với level cao hơn"]
      }
    ]
  }
}
```

### 3. Quiz Comparison
**GET** `/quiz-comparison`

So sánh performance giữa các quiz.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz-comparison`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Query Parameters:**
  - `quiz_ids` (string, optional): Danh sách quiz_id cách nhau bởi dấu phẩy (ví dụ: "1,2,3")
  - `subject_id` (integer, optional): So sánh tất cả quiz trong subject
  - **Note:** Phải có ít nhất một trong hai parameters

**Response Structure:**
```typescript
interface QuizComparisonResponse {
  success: boolean;
  data: {
    quiz_comparisons: Array<{
      quiz_id: number;
      quiz_name: string;
      subject_name: string;
      total_participants: number;
      average_score: number;
      completion_rate: number;
      overall_accuracy: number;
      pass_rate: number;
      excellence_rate: number;
    }>;
    comparison_insights: Array<{
      type: "performance_comparison" | "participation_comparison" | "difficulty_comparison";
      message: string;
      priority?: "high" | "medium" | "low";
    }>;
    summary: {
      best_performing_quiz: {
        quiz_id: number;
        quiz_name: string;
        average_score: number;
        metric: string;
      };
      worst_performing_quiz: {
        quiz_id: number;
        quiz_name: string;
        average_score: number;
        metric: string;
      };
      overall_insights: string[];
    };
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "quiz_comparisons": [
      {
        "quiz_id": 122,
        "quiz_name": "Bài tập Thiết kế web",
        "subject_name": "Thiết kế web",
        "total_participants": 2,
        "average_score": 8.33,
        "completion_rate": 100.0,
        "overall_accuracy": 83.33,
        "pass_rate": 100.0,
        "excellence_rate": 50.0
      },
      {
        "quiz_id": 123,
        "quiz_name": "Quiz HTML/CSS",
        "subject_name": "Thiết kế web",
        "total_participants": 15,
        "average_score": 7.2,
        "completion_rate": 93.3,
        "overall_accuracy": 72.0,
        "pass_rate": 86.7,
        "excellence_rate": 20.0
      }
    ],
    "comparison_insights": [
      {
        "type": "performance_comparison",
        "message": "Quiz 122 có điểm trung bình cao nhất (8.33)",
        "priority": "medium"
      },
      {
        "type": "participation_comparison",
        "message": "Quiz 123 có nhiều học sinh tham gia hơn (15 vs 2)"
      }
    ],
    "summary": {
      "best_performing_quiz": {
        "quiz_id": 122,
        "quiz_name": "Bài tập Thiết kế web",
        "average_score": 8.33,
        "metric": "average_score"
      },
      "worst_performing_quiz": {
        "quiz_id": 123,
        "quiz_name": "Quiz HTML/CSS",
        "average_score": 7.2,
        "metric": "average_score"
      },
      "overall_insights": [
        "Có sự chênh lệch về chất lượng giữa các quiz",
        "Cần xem xét cân bằng độ khó"
      ]
    }
  }
}
```

### 4. Student Detailed Analysis
**GET** `/quiz/:quizId/student/:userId/detailed-analysis`

Phân tích chi tiết cá nhân học sinh với insights từng câu hỏi.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz/{quizId}/student/{userId}/detailed-analysis`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz
  - `userId` (integer, required): ID của học sinh cần phân tích

**Response Structure:**
```typescript
interface StudentDetailedAnalysisResponse {
  success: boolean;
  data: {
    student_info: {
      user_id: number;
      name: string;
      email: string;
    };
    performance_summary: {
      score: number;
      percentage_score: number;
      accuracy: number;
      total_questions: number;
      correct_answers: number;
      completion_time: number;
      average_time_per_question: number;
    };
    overall_insights: {
      performance_level: "excellent" | "good" | "average" | "weak";
      insights: string[];
      recommendations: string[];
    };
    learning_outcome_analysis: Array<{
      lo_id: number;
      lo_name: string;
      questions_attempted: number;
      correct_answers: number;
      accuracy: number;
      performance_level: string;
      insights: string[];
      recommendations: string[];
    }>;
    question_by_question_analysis: Array<{
      question_id: number;
      question_text: string;
      lo_name: string;
      level_name: string;
      is_correct: boolean;
      selected_answer: number | null;
      correct_answer: number;
      time_spent: number;
      insights: string[];
      recommendations: string[];
    }>;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 120,
      "name": "Huỳnh Phan Vân Anh",
      "email": "<EMAIL>"
    },
    "performance_summary": {
      "score": 9.17,
      "percentage_score": 91.67,
      "accuracy": 91.67,
      "total_questions": 24,
      "correct_answers": 22,
      "completion_time": 151,
      "average_time_per_question": 9757.92
    },
    "overall_insights": {
      "performance_level": "excellent",
      "insights": ["Kết quả xuất sắc - học sinh nắm vững kiến thức"],
      "recommendations": ["Tiếp tục duy trì và phát triển"]
    },
    "learning_outcome_analysis": [
      {
        "lo_id": 1,
        "lo_name": "Thiết kế giao diện",
        "questions_attempted": 16,
        "correct_answers": 15,
        "accuracy": 93.75,
        "performance_level": "excellent",
        "insights": ["Nắm vững LO này"],
        "recommendations": ["Tiếp tục phát triển kỹ năng"]
      }
    ],
    "question_by_question_analysis": [
      {
        "question_id": 11,
        "question_text": "HTML là viết tắt của gì?",
        "lo_name": "Kiến thức cơ bản",
        "level_name": "Nhận biết",
        "is_correct": true,
        "selected_answer": 1,
        "correct_answer": 1,
        "time_spent": 21117,
        "insights": ["Trả lời đúng nhưng hơi chậm"],
        "recommendations": ["Luyện tập để tăng tốc độ"]
      }
    ]
  }
}
```

### 5. Teaching Insights
**GET** `/quiz/:quizId/teaching-insights`

Lấy insights và recommendations tổng hợp cho giảng viên.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz/{quizId}/teaching-insights`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz cần phân tích

**Response Structure:**
```typescript
interface TeachingInsightsResponse {
  success: boolean;
  data: {
    summary_insights: {
      overall_assessment: "excellent" | "good" | "mixed" | "needs_improvement";
      key_strengths: string[];
      main_challenges: string[];
      immediate_actions_needed: number;
      priority_level: "high" | "medium" | "low";
    };
    detailed_insights: {
      curriculum_insights: Array<{
        type: "strength" | "weakness" | "opportunity" | "threat";
        message: string;
        impact: "high" | "medium" | "low";
        affected_los?: string[];
      }>;
      teaching_method_insights: Array<{
        type: "strength" | "weakness" | "opportunity" | "improvement";
        message: string;
        impact: "high" | "medium" | "low";
        evidence?: string[];
      }>;
      student_insights: Array<{
        type: "performance" | "engagement" | "difficulty" | "progress";
        message: string;
        affected_groups?: string[];
        impact: "high" | "medium" | "low";
      }>;
      action_recommendations: Array<{
        category: "curriculum_revision" | "teaching_method" | "student_support" | "assessment_adjustment";
        action: string;
        priority: "high" | "medium" | "low";
        timeline: "immediate" | "short_term" | "long_term";
        expected_impact: string;
      }>;
    };
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "summary_insights": {
      "overall_assessment": "good",
      "key_strengths": ["Tỷ lệ hoàn thành cao", "Học sinh nắm vững kiến thức cơ bản"],
      "main_challenges": ["Cần tăng độ khó để thách thức học sinh"],
      "immediate_actions_needed": 1,
      "priority_level": "medium"
    },
    "detailed_insights": {
      "curriculum_insights": [
        {
          "type": "strength",
          "message": "Nội dung phù hợp với trình độ học sinh",
          "impact": "high",
          "affected_los": ["Thiết kế giao diện", "HTML/CSS cơ bản"]
        }
      ],
      "teaching_method_insights": [
        {
          "type": "opportunity",
          "message": "Có thể bổ sung thêm câu hỏi thực hành",
          "impact": "medium",
          "evidence": ["Học sinh làm tốt câu hỏi lý thuyết"]
        }
      ],
      "student_insights": [
        {
          "type": "performance",
          "message": "Phân hóa rõ ràng giữa học sinh giỏi và trung bình",
          "affected_groups": ["excellent", "good"],
          "impact": "medium"
        }
      ],
      "action_recommendations": [
        {
          "category": "assessment_adjustment",
          "action": "Thêm câu hỏi nâng cao cho học sinh giỏi",
          "priority": "medium",
          "timeline": "short_term",
          "expected_impact": "Tăng thách thức và động lực học tập"
        }
      ]
    }
  }
}
```

### 6. Quiz Benchmark
**GET** `/quiz/:quizId/benchmark`

Lấy benchmark và so sánh với historical data.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/quiz/{quizId}/benchmark`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz cần benchmark
- **Query Parameters:**
  - `compare_with_subject` (boolean, optional): So sánh với quiz khác trong subject (default: true)
  - `compare_with_teacher` (boolean, optional): So sánh với quiz của cùng giảng viên (default: true)

**Response Structure:**
```typescript
interface QuizBenchmarkResponse {
  success: boolean;
  data: {
    current_quiz: {
      quiz_id: number;
      name: string;
      subject_name: string;
      metrics: {
        total_participants: number;
        average_score: number;
        completion_rate: number;
        pass_rate: number;
        excellence_rate: number;
        overall_accuracy: number;
      };
    };
    comparisons: {
      subject_benchmark?: {
        comparison_base: string;
        total_compared_quizzes: number;
        subject_average: {
          average_score: number;
          completion_rate: number;
          pass_rate: number;
          excellence_rate: number;
        };
        current_vs_average: {
          score_difference: number;
          completion_difference: number;
          pass_rate_difference: number;
          excellence_difference: number;
        };
      };
      teacher_benchmark?: {
        comparison_base: string;
        total_compared_quizzes: number;
        teacher_average: {
          average_score: number;
          completion_rate: number;
          pass_rate: number;
          excellence_rate: number;
        };
        current_vs_average: {
          score_difference: number;
          completion_difference: number;
          pass_rate_difference: number;
          excellence_difference: number;
        };
      };
    };
    performance_ranking: {
      subject_rank: number;
      total_subject_quizzes: number;
      subject_percentile: number;
      teacher_rank?: number;
      total_teacher_quizzes?: number;
      teacher_percentile?: number;
      ranking_insights: string;
    };
    insights: Array<{
      type: "positive" | "negative" | "neutral" | "warning";
      category: "performance" | "participation" | "difficulty" | "comparison";
      message: string;
      impact?: "high" | "medium" | "low";
    }>;
    recommendations: Array<{
      category: "improvement" | "maintenance" | "adjustment" | "investigation";
      suggestion: string;
      priority: "high" | "medium" | "low";
      rationale: string;
    }>;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "current_quiz": {
      "quiz_id": 122,
      "name": "Bài tập Thiết kế web",
      "subject_name": "Thiết kế web",
      "metrics": {
        "total_participants": 2,
        "average_score": 8.33,
        "completion_rate": 100.0,
        "pass_rate": 100.0,
        "excellence_rate": 50.0,
        "overall_accuracy": 83.33
      }
    },
    "comparisons": {
      "subject_benchmark": {
        "comparison_base": "5 quiz khác trong subject Thiết kế web",
        "total_compared_quizzes": 5,
        "subject_average": {
          "average_score": 7.2,
          "completion_rate": 85.4,
          "pass_rate": 78.2,
          "excellence_rate": 25.6
        },
        "current_vs_average": {
          "score_difference": 1.13,
          "completion_difference": 14.6,
          "pass_rate_difference": 21.8,
          "excellence_difference": 24.4
        }
      }
    },
    "performance_ranking": {
      "subject_rank": 1,
      "total_subject_quizzes": 6,
      "subject_percentile": 100.0,
      "ranking_insights": "Top performer trong subject"
    },
    "insights": [
      {
        "type": "positive",
        "category": "performance",
        "message": "Quiz này có điểm trung bình cao hơn 1.13 điểm so với trung bình subject",
        "impact": "high"
      },
      {
        "type": "positive",
        "category": "participation",
        "message": "Tỷ lệ hoàn thành 100% - cao hơn trung bình 14.6%",
        "impact": "medium"
      }
    ],
    "recommendations": [
      {
        "category": "maintenance",
        "suggestion": "Duy trì phương pháp giảng dạy hiện tại",
        "priority": "medium",
        "rationale": "Quiz đạt kết quả tốt so với các quiz khác"
      },
      {
        "category": "improvement",
        "suggestion": "Chia sẻ phương pháp với các giảng viên khác",
        "priority": "low",
        "rationale": "Có thể giúp nâng cao chất lượng chung của subject"
      }
    ]
  }
}
```

### 7. Debug Quiz Data (Development Only)
**GET** `/debug/:quizId`

API debug để kiểm tra dữ liệu quiz và troubleshoot các vấn đề.

**Request:**
- **Method:** GET
- **URL:** `/api/teacher-analytics/debug/{quizId}`
- **Headers:**
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```
- **Path Parameters:**
  - `quizId` (integer, required): ID của quiz cần debug

**Response Structure:**
```typescript
interface DebugQuizDataResponse {
  success: boolean;
  debug_data: {
    quiz_info: {
      quiz_id: number;
      name: string;
      subject_name: string;
      total_questions: number;
    };
    data_counts: {
      quiz_results: number;
      question_history: number;
    };
    sample_quiz_results: Array<{
      user_id: number;
      student_name: string;
      score: number;
      status: string;
      completion_time: number;
    }>;
    sample_question_history: Array<{
      user_id: number;
      question_id: number;
      is_correct: boolean;
      time_spent: number;
    }>;
    student_groups_count: {
      excellent: number;
      good: number;
      average: number;
      weak: number;
    };
    sample_students_by_group: {
      excellent: Array<any>;
      good: Array<any>;
      average: Array<any>;
      weak: Array<any>;
    };
  };
}
```

## Error Responses

Tất cả API trả về error theo format:
```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

**Common Error Examples:**
```json
// Quiz không tồn tại
{
  "success": false,
  "error": "Quiz not found",
  "details": "Quiz with ID 999 does not exist"
}

// Không có quyền truy cập
{
  "success": false,
  "error": "Access denied",
  "details": "Only admin and teacher roles can access this endpoint"
}

// Tham số không hợp lệ
{
  "success": false,
  "error": "Invalid parameters",
  "details": "groupType must be one of: excellent, good, average, weak"
}

// Không có dữ liệu
{
  "success": false,
  "error": "No data available",
  "details": "Quiz has no participants or question history"
}
```

## Status Codes
- `200`: Success
- `400`: Bad Request (tham số không hợp lệ)
- `401`: Unauthorized (chưa đăng nhập)
- `403`: Forbidden (không có quyền truy cập)
- `404`: Not Found (không tìm thấy resource)
- `500`: Internal Server Error

## Use Cases & Integration Examples

### 1. Đánh giá tổng quan quiz
```javascript
// Frontend integration example
const fetchQuizReport = async (quizId) => {
  try {
    const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/comprehensive-report`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Process data for UI
    const { quiz_info, overall_performance, student_groups, teacher_insights } = data.data;

    // Update UI components
    updateQuizOverview(quiz_info, overall_performance);
    updateStudentGroupsChart(student_groups);
    displayTeacherInsights(teacher_insights);

    return data;
  } catch (error) {
    console.error('Error fetching quiz report:', error);
    showErrorMessage('Không thể tải báo cáo quiz');
  }
};
```

### 2. Phân tích nhóm học sinh yếu
```javascript
// Xem chi tiết nhóm học sinh yếu
const analyzeWeakStudents = async (quizId) => {
  const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/student-groups/weak`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  const weakStudents = await response.json();

  if (weakStudents.success && weakStudents.data.students.length > 0) {
    // Display weak students with recommendations
    displayStudentList(weakStudents.data.students);
    showRecommendations(weakStudents.data.group_overview.recommendations);
  } else {
    showMessage('Không có học sinh nào trong nhóm yếu');
  }
};
```

### 3. So sánh với quiz khác
```javascript
// So sánh với các quiz trong subject
const compareQuizzes = async (subjectId) => {
  const response = await fetch(`/api/teacher-analytics/quiz-comparison?subject_id=${subjectId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  const comparison = await response.json();

  // Create comparison chart
  const chartData = comparison.data.quiz_comparisons.map(quiz => ({
    name: quiz.quiz_name,
    score: quiz.average_score,
    completion: quiz.completion_rate,
    participants: quiz.total_participants
  }));

  renderComparisonChart(chartData);
  displayInsights(comparison.data.comparison_insights);
};
```

### 4. Lấy insights giảng dạy
```javascript
// Lấy recommendations cho giảng viên
const getTeachingInsights = async (quizId) => {
  const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/teaching-insights`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  const insights = await response.json();

  // Organize insights by priority
  const highPriorityActions = insights.data.detailed_insights.action_recommendations
    .filter(action => action.priority === 'high');

  // Display urgent actions first
  displayUrgentActions(highPriorityActions);
  displayAllInsights(insights.data);
};
```

### 5. Error Handling Pattern
```javascript
// Reusable error handling function
const handleApiError = (error, response) => {
  if (response?.status === 401) {
    // Redirect to login
    window.location.href = '/login';
  } else if (response?.status === 403) {
    showErrorMessage('Bạn không có quyền truy cập chức năng này');
  } else if (response?.status === 404) {
    showErrorMessage('Không tìm thấy dữ liệu quiz');
  } else {
    showErrorMessage('Có lỗi xảy ra, vui lòng thử lại sau');
  }

  console.error('API Error:', error);
};
```

### 6. TypeScript Integration
```typescript
// Type definitions for frontend
interface QuizAnalyticsService {
  getComprehensiveReport(quizId: number): Promise<ComprehensiveQuizReportResponse>;
  getStudentGroupAnalysis(quizId: number, groupType: StudentGroupType): Promise<StudentGroupAnalysisResponse>;
  compareQuizzes(params: QuizComparisonParams): Promise<QuizComparisonResponse>;
  getStudentAnalysis(quizId: number, userId: number): Promise<StudentDetailedAnalysisResponse>;
  getTeachingInsights(quizId: number): Promise<TeachingInsightsResponse>;
  getBenchmark(quizId: number, options?: BenchmarkOptions): Promise<QuizBenchmarkResponse>;
}

type StudentGroupType = 'excellent' | 'good' | 'average' | 'weak';

interface QuizComparisonParams {
  quiz_ids?: string;
  subject_id?: number;
}

interface BenchmarkOptions {
  compare_with_subject?: boolean;
  compare_with_teacher?: boolean;
}
```

## Performance Considerations

### 1. Caching Strategy
- Cache quiz reports for 5-10 minutes
- Invalidate cache when new quiz results are submitted
- Use Redis or in-memory cache for frequently accessed data

### 2. Pagination
- For large student lists, implement pagination
- Default page size: 20 students per page
- Include total count in response

### 3. Rate Limiting
- Limit to 100 requests per minute per user
- Implement exponential backoff for failed requests

### 4. Data Optimization
- Use database indexes on quiz_id, user_id, subject_id
- Consider pre-calculating analytics for popular quizzes
- Implement lazy loading for detailed analysis

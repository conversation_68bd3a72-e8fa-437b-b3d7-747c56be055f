# Teacher Analytics API Documentation

## Tổ<PERSON> quan
Bộ API Teacher Analytics cung cấp các công cụ phân tích chuyên sâu cho giảng viên để đánh giá hiệu quả quiz và hiểu rõ tình hình học tập của học sinh.

## Base URL
```
/api/teacher-analytics
```

## Authentication
Tất cả API yêu cầu authentication token và chỉ dành cho role `admin` và `teacher`.

## API Endpoints

### 1. Comprehensive Quiz Report
**GET** `/quiz/:quizId/comprehensive-report`

<PERSON><PERSON>y báo cáo tổng quan chi tiết về quiz với phân tích điểm mạnh/yếu và insights.

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 1,
      "name": "Quiz <PERSON><PERSON> h<PERSON>",
      "total_questions": 20
    },
    "overall_performance": {
      "total_participants": 45,
      "average_score": 72.5,
      "completion_rate": 88.9
    },
    "learning_outcome_analysis": [
      {
        "lo_name": "Gi<PERSON>i phương trình",
        "accuracy": 65.2,
        "performance_level": "average",
        "insights": ["LO trung bình - cần cải thiện"],
        "recommendations": ["Tăng cường luyện tập"]
      }
    ],
    "student_groups": {
      "excellent": { "count": 12, "percentage": 26.7 },
      "good": { "count": 18, "percentage": 40.0 },
      "average": { "count": 10, "percentage": 22.2 },
      "weak": { "count": 5, "percentage": 11.1 }
    },
    "teacher_insights": [
      {
        "category": "strengths",
        "message": "Học sinh nắm vững LO cơ bản",
        "priority": "maintain"
      }
    ]
  }
}
```

### 2. Student Group Analysis
**GET** `/quiz/:quizId/student-groups/:groupType`

Phân tích chi tiết theo nhóm học sinh (excellent, good, average, weak).

**Parameters:**
- `groupType`: excellent | good | average | weak

**Response:**
```json
{
  "success": true,
  "data": {
    "group_overview": {
      "group_name": "weak",
      "student_count": 5,
      "score_range": { "min": 25, "max": 45, "average": 35.2 },
      "insights": ["Nhóm cần hỗ trợ đặc biệt"],
      "recommendations": [
        {
          "type": "immediate_action",
          "suggestion": "Tổ chức buổi ôn tập riêng",
          "priority": "high"
        }
      ]
    },
    "students": [
      {
        "user_id": 123,
        "name": "Nguyễn Văn A",
        "score": 35,
        "correct_answers": 7
      }
    ],
    "learning_outcome_analysis": [...],
    "difficulty_level_analysis": [...]
  }
}
```

### 3. Quiz Comparison
**GET** `/quiz-comparison`

So sánh performance giữa các quiz.

**Query Parameters:**
- `quiz_ids`: Danh sách quiz_id cách nhau bởi dấu phẩy (ví dụ: 1,2,3)
- `subject_id`: So sánh tất cả quiz trong subject

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_comparisons": [
      {
        "quiz_id": 1,
        "quiz_name": "Quiz 1",
        "average_score": 72.5,
        "completion_rate": 88.9,
        "overall_accuracy": 65.2
      }
    ],
    "comparison_insights": [
      {
        "type": "performance_comparison",
        "message": "Quiz 1 có điểm cao nhất (72.5)"
      }
    ],
    "summary": {
      "best_performing_quiz": {...},
      "worst_performing_quiz": {...}
    }
  }
}
```

### 4. Student Detailed Analysis
**GET** `/quiz/:quizId/student/:userId/detailed-analysis`

Phân tích chi tiết cá nhân học sinh với insights từng câu hỏi.

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>"
    },
    "performance_summary": {
      "score": 65,
      "accuracy": 65.0,
      "total_questions": 20,
      "correct_answers": 13
    },
    "overall_insights": {
      "insights": ["Kết quả trung bình - có thể cải thiện"],
      "recommendations": ["Tăng cường luyện tập"]
    },
    "question_by_question_analysis": [
      {
        "question_id": 1,
        "question_text": "2 + 2 = ?",
        "is_correct": true,
        "time_spent": 45,
        "insights": ["Trả lời nhanh và chính xác"],
        "recommendations": []
      }
    ]
  }
}
```

### 5. Teaching Insights
**GET** `/quiz/:quizId/teaching-insights`

Lấy insights và recommendations tổng hợp cho giảng viên.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary_insights": {
      "overall_assessment": "mixed",
      "key_strengths": ["Điểm trung bình tốt"],
      "main_challenges": ["2 LO cần cải thiện"],
      "immediate_actions_needed": 2
    },
    "detailed_insights": {
      "curriculum_insights": [
        {
          "type": "weakness",
          "message": "2 LO có hiệu suất thấp",
          "impact": "high"
        }
      ],
      "teaching_method_insights": [...],
      "student_insights": [...],
      "action_recommendations": [
        {
          "category": "curriculum_revision",
          "action": "Xem xét điều chỉnh nội dung giảng dạy",
          "priority": "high",
          "timeline": "immediate"
        }
      ]
    }
  }
}
```

### 6. Quiz Benchmark
**GET** `/quiz/:quizId/benchmark`

Lấy benchmark và so sánh với historical data.

**Query Parameters:**
- `compare_with_subject`: So sánh với quiz khác trong subject (default: true)
- `compare_with_teacher`: So sánh với quiz của cùng giảng viên (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_quiz": {
      "quiz_id": 1,
      "name": "Quiz Toán",
      "metrics": {
        "average_score": 72.5,
        "completion_rate": 88.9,
        "pass_rate": 75.6,
        "excellence_rate": 26.7
      }
    },
    "comparisons": {
      "subject_benchmark": {
        "comparison_base": "8 quiz khác trong subject",
        "subject_average": {
          "average_score": 68.2,
          "completion_rate": 82.1
        },
        "current_vs_average": {
          "score_difference": 4.3,
          "completion_difference": 6.8
        }
      }
    },
    "performance_ranking": {
      "current_rank": 2,
      "total_quizzes": 9,
      "percentile": 88.9,
      "ranking_insights": "Top performer"
    },
    "insights": [
      {
        "type": "positive",
        "category": "performance",
        "message": "Quiz này có điểm cao hơn 4.3 điểm so với trung bình"
      }
    ],
    "recommendations": [
      {
        "category": "improvement",
        "suggestion": "Duy trì phương pháp hiện tại",
        "priority": "medium"
      }
    ]
  }
}
```

## Error Responses

Tất cả API trả về error theo format:
```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Status Codes
- `200`: Success
- `400`: Bad Request (tham số không hợp lệ)
- `401`: Unauthorized (chưa đăng nhập)
- `403`: Forbidden (không có quyền truy cập)
- `404`: Not Found (không tìm thấy resource)
- `500`: Internal Server Error

## Use Cases

### 1. Đánh giá tổng quan quiz
```javascript
// Lấy báo cáo tổng quan
const response = await fetch('/api/teacher-analytics/quiz/123/comprehensive-report');
const data = await response.json();
```

### 2. Phân tích nhóm học sinh yếu
```javascript
// Xem chi tiết nhóm học sinh yếu
const response = await fetch('/api/teacher-analytics/quiz/123/student-groups/weak');
const weakStudents = await response.json();
```

### 3. So sánh với quiz khác
```javascript
// So sánh với các quiz trong subject
const response = await fetch('/api/teacher-analytics/quiz-comparison?subject_id=5');
const comparison = await response.json();
```

### 4. Lấy insights giảng dạy
```javascript
// Lấy recommendations cho giảng viên
const response = await fetch('/api/teacher-analytics/quiz/123/teaching-insights');
const insights = await response.json();
```

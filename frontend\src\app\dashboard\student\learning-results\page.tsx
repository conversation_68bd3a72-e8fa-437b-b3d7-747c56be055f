"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  Award,
  Lightbulb,
  BarChart3,
  BookOpen,
  Target,
} from "lucide-react";
import {
  StudentScoreAnalysisChart,
  StudentLearningOutcomeMasteryChart,
  StudentImprovementSuggestionsChart,
} from "@/components/charts";

export default function StudentLearningResultsPage() {
  return (
    <div className="w-full mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 mb-6 sm:mb-8 md:mb-10">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold"><PERSON><PERSON><PERSON> q<PERSON><PERSON> tậ<PERSON></h1>
          <p className="text-muted-foreground mt-1">
            <PERSON><PERSON> tích chi tiết về tiến độ học tập và gợi ý cải thiện cá nhân
          </p>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="border-2 hover:border-primary/50 transition-all">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Phân tích Điểm số
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Xem chi tiết về hiệu suất học tập, điểm mạnh và điểm yếu của bạn
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 hover:border-primary/50 transition-all">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Award className="h-5 w-5 text-green-600" />
              Mức độ Thành thạo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Theo dõi tiến độ thành thạo các Learning Outcomes
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 hover:border-primary/50 transition-all">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              Gợi ý Cải thiện
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Nhận gợi ý cá nhân hóa để cải thiện kết quả học tập
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Dashboard */}
      <div className="mb-8">
        <Tabs defaultValue="analysis" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger
              value="analysis"
              className="flex items-center gap-2 text-sm"
            >
              <BarChart3 className="h-4 w-4" />
              Phân tích Điểm số
            </TabsTrigger>
            <TabsTrigger
              value="mastery"
              className="flex items-center gap-2 text-sm"
            >
              <Target className="h-4 w-4" />
              Mức độ Thành thạo
            </TabsTrigger>
            <TabsTrigger
              value="suggestions"
              className="flex items-center gap-2 text-sm"
            >
              <Lightbulb className="h-4 w-4" />
              Gợi ý Cải thiện
            </TabsTrigger>
          </TabsList>

          <TabsContent value="analysis" className="space-y-6 mt-6">
            <div className="mb-4 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <h3 className="font-medium text-blue-900 mb-1">
                Phân tích Điểm số Tổng thể
              </h3>
              <p className="text-sm text-blue-700">
                Phân tích toàn diện về hiệu suất học tập của bạn với điểm mạnh, 
                điểm yếu và so sánh với trung bình lớp.
              </p>
            </div>
            <StudentScoreAnalysisChart className="w-full" />
          </TabsContent>

          <TabsContent value="mastery" className="space-y-6 mt-6">
            <div className="mb-4 p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
              <h3 className="font-medium text-green-900 mb-1">
                Mức độ Thành thạo Learning Outcomes
              </h3>
              <p className="text-sm text-green-700">
                Xem mức độ thành thạo của bạn theo từng Learning Outcome và 
                nhận gợi ý cải thiện cụ thể.
              </p>
            </div>
            <StudentLearningOutcomeMasteryChart className="w-full" />
          </TabsContent>

          <TabsContent value="suggestions" className="space-y-6 mt-6">
            <div className="mb-4 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
              <h3 className="font-medium text-yellow-900 mb-1">
                Gợi ý Cải thiện Cá nhân
              </h3>
              <p className="text-sm text-yellow-700">
                Nhận gợi ý cải thiện được cá nhân hóa dựa trên kết quả học tập 
                và điểm yếu của bạn.
              </p>
            </div>
            <StudentImprovementSuggestionsChart className="w-full" />
          </TabsContent>
        </Tabs>
      </div>

      {/* Additional Info */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <BookOpen className="h-6 w-6 text-blue-600 mt-1" />
            <div>
              <h3 className="font-medium text-blue-900 mb-2">
                Cách sử dụng hiệu quả
              </h3>
              <div className="space-y-2 text-sm text-blue-700">
                <p>• <strong>Phân tích Điểm số:</strong> Xem tổng quan về hiệu suất và so sánh với lớp</p>
                <p>• <strong>Mức độ Thành thạo:</strong> Theo dõi tiến độ học từng Learning Outcome</p>
                <p>• <strong>Gợi ý Cải thiện:</strong> Thực hiện các hành động được đề xuất để nâng cao kết quả</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
